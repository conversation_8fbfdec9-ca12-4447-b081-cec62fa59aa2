# Redis持久化面试题

## 1. RDB和AOF的区别？

**RDB（Redis Database）**：
- **原理**：定期生成数据快照，保存到磁盘
- **优点**：
  - 文件紧凑，占用空间小
  - 恢复速度快
  - 对Redis性能影响小
  - 适合备份和灾难恢复
- **缺点**：
  - 可能丢失最后一次快照后的数据
  - fork子进程时会阻塞主进程
- **适用场景**：对数据完整性要求不高，注重性能的场景

**AOF（Append Only File）**：
- **原理**：记录每个写操作命令到日志文件
- **优点**：
  - 数据完整性好，最多丢失1秒数据
  - 日志文件可读性强，便于分析
  - 支持秒级数据恢复
- **缺点**：
  - 文件体积大
  - 恢复速度慢
  - 对Redis性能影响较大
- **适用场景**：对数据完整性要求高的场景

## 2. AOF重写机制？

**触发条件**：
- AOF文件大小超过配置的阈值
- AOF文件增长比例超过配置值
- 手动执行BGREWRITEAOF命令

**重写过程**：
1. **fork子进程**：主进程fork出子进程进行重写
2. **生成新AOF**：子进程根据内存数据生成新的AOF文件
3. **缓冲写入**：主进程继续处理命令，写入AOF缓冲区和重写缓冲区
4. **文件替换**：重写完成后，用新AOF文件替换旧文件
5. **缓冲同步**：将重写期间的命令追加到新AOF文件

**重写优化**：
- 合并相同key的多个操作
- 删除过期的key
- 压缩冗余命令

## 3. RDB快照的生成过程？

**触发方式**：
- **自动触发**：满足save配置条件
- **手动触发**：执行SAVE或BGSAVE命令
- **其他触发**：主从复制、FLUSHALL等

**BGSAVE过程**：
1. **检查状态**：检查是否有子进程在执行
2. **fork子进程**：主进程fork出子进程
3. **生成快照**：子进程将内存数据写入临时RDB文件
4. **原子替换**：完成后用临时文件替换旧RDB文件
5. **清理资源**：子进程退出，主进程继续服务

**fork的影响**：
- 内存使用量瞬间翻倍（写时复制）
- fork过程中主进程会阻塞

## 4. 混合持久化？

**Redis 4.0引入**：
- 结合RDB和AOF的优点
- RDB作为基础数据，AOF记录增量变化

**工作原理**：
1. AOF重写时，先写入RDB格式的数据
2. 然后追加AOF格式的增量数据
3. 恢复时先加载RDB部分，再重放AOF部分

**优势**：
- 恢复速度快（RDB部分）
- 数据完整性好（AOF部分）
- 文件体积相对较小

## 5. 持久化配置优化？

**RDB配置**：
```
# 900秒内至少1个key变化就保存
save 900 1
# 300秒内至少10个key变化就保存  
save 300 10
# 60秒内至少10000个key变化就保存
save 60 10000

# RDB文件压缩
rdbcompression yes
# RDB文件校验
rdbchecksum yes
```

**AOF配置**：
```
# 开启AOF
appendonly yes
# 同步策略：always/everysec/no
appendfsync everysec
# 重写触发条件
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
```

**混合持久化配置**：
```
# 开启混合持久化
aof-use-rdb-preamble yes
```

## 6. 持久化的性能影响？

**RDB性能影响**：
- fork时短暂阻塞（毫秒级）
- 子进程CPU和内存消耗
- 磁盘IO压力

**AOF性能影响**：
- 每次写操作都要记录日志
- fsync系统调用的开销
- AOF重写时的额外开销

**优化建议**：
- 合理设置save参数
- 选择合适的appendfsync策略
- 在业务低峰期进行AOF重写
- 使用SSD提高磁盘IO性能

## 7. 数据恢复流程？

**启动时恢复顺序**：
1. 检查是否存在AOF文件
2. 如果AOF存在且开启，优先使用AOF恢复
3. 如果AOF不存在，使用RDB文件恢复
4. 如果都不存在，启动空实例

**恢复时间对比**：
- RDB恢复：快，适合大数据量
- AOF恢复：慢，但数据更完整
- 混合持久化：兼顾速度和完整性

## 8. 持久化故障处理？

**AOF文件损坏**：
- 使用redis-check-aof工具修复
- 备份损坏文件后再修复
- 可能会丢失部分数据

**RDB文件损坏**：
- 使用redis-check-rdb工具检查
- 如果无法修复，使用备份文件
- 考虑从主从复制中恢复

**磁盘空间不足**：
- 监控磁盘使用率
- 及时清理过期的备份文件
- 考虑数据压缩和归档

## 9. 持久化最佳实践？

**生产环境建议**：
- 主库关闭RDB，开启AOF
- 从库开启RDB，用于备份
- 定期备份RDB文件到其他机器
- 监控持久化相关指标

**配置建议**：
- appendfsync设置为everysec
- 开启混合持久化
- 合理设置AOF重写参数
- 在独立磁盘上存储持久化文件

**监控指标**：
- RDB生成频率和耗时
- AOF文件大小和重写频率
- fork操作的耗时
- 磁盘IO使用率
