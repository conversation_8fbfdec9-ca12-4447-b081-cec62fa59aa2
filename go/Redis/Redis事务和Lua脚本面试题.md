# Redis事务和Lua脚本面试题

## 1. Redis事务机制

### 事务基本概念
Redis事务是一组命令的集合，具有以下特性：
- **原子性**：事务中的命令要么全部执行，要么全部不执行
- **隔离性**：事务执行期间不会被其他客户端命令打断
- **一致性**：事务执行前后数据保持一致
- **持久性**：取决于Redis的持久化配置

### 事务命令
- **MULTI**：开始事务
- **EXEC**：执行事务
- **DISCARD**：取消事务
- **WATCH**：监控key的变化

### 事务执行流程
1. 客户端发送MULTI命令
2. 客户端发送一系列命令（命令入队）
3. 客户端发送EXEC命令（执行事务）

## 2. 事务的特点和限制

### 事务特点
- **命令排队**：MULTI后的命令会排队等待执行
- **原子执行**：EXEC时一次性执行所有命令
- **错误处理**：语法错误会导致整个事务取消，运行时错误不会回滚

### 事务限制
- **不支持回滚**：某个命令执行失败不会回滚其他命令
- **不支持嵌套**：事务内不能再开启事务
- **WATCH机制**：只能在MULTI之前使用

### 面试要点
**Q: Redis事务支持回滚吗？**
**答案**：不支持。Redis认为命令失败是编程错误，应该在开发阶段发现。

## 3. WATCH机制

### 乐观锁实现
WATCH命令用于实现乐观锁，监控key的变化：
- 如果被监控的key在事务执行前被修改，事务会被取消
- 适用于解决并发修改问题

### 使用场景
```
# 场景：扣减库存
WATCH stock:item:1001
GET stock:item:1001
# 检查库存是否足够
MULTI
DECR stock:item:1001
EXEC
```

### 面试要点
**Q: WATCH如何解决并发问题？**
**答案**：通过版本检查机制，如果key被其他客户端修改，事务会失败。

## 4. Lua脚本

### Lua脚本优势
- **原子性**：脚本执行期间不会被打断
- **减少网络开销**：多个命令一次性执行
- **逻辑复杂性**：支持条件判断、循环等
- **可复用性**：脚本可以被缓存和重复使用

### 基本语法
```lua
-- 获取参数
local key = KEYS[1]
local value = ARGV[1]

-- 执行Redis命令
local result = redis.call('GET', key)

-- 条件判断
if result == false then
    redis.call('SET', key, value)
    return 1
else
    return 0
end
```

### 执行命令
- **EVAL**：执行Lua脚本
- **EVALSHA**：执行已缓存的脚本
- **SCRIPT LOAD**：加载脚本到缓存
- **SCRIPT EXISTS**：检查脚本是否存在

## 5. 常见应用场景

### 分布式锁
```lua
-- 加锁脚本
if redis.call('SET', KEYS[1], ARGV[1], 'PX', ARGV[2], 'NX') then
    return 1
else
    return 0
end

-- 解锁脚本
if redis.call('GET', KEYS[1]) == ARGV[1] then
    return redis.call('DEL', KEYS[1])
else
    return 0
end
```

### 限流算法
```lua
-- 滑动窗口限流
local key = KEYS[1]
local window = tonumber(ARGV[1])
local limit = tonumber(ARGV[2])
local current = tonumber(ARGV[3])

-- 清理过期数据
redis.call('ZREMRANGEBYSCORE', key, 0, current - window)

-- 获取当前计数
local count = redis.call('ZCARD', key)

if count < limit then
    -- 添加当前请求
    redis.call('ZADD', key, current, current)
    redis.call('EXPIRE', key, window)
    return 1
else
    return 0
end
```

### 原子计数器
```lua
-- 带过期时间的计数器
local key = KEYS[1]
local expire = tonumber(ARGV[1])
local increment = tonumber(ARGV[2])

local current = redis.call('GET', key)
if current == false then
    redis.call('SET', key, increment)
    redis.call('EXPIRE', key, expire)
    return increment
else
    return redis.call('INCRBY', key, increment)
end
```

## 6. 面试高频问题

### Q1: Redis事务和关系数据库事务的区别？
**答案**：
- **原子性**：Redis事务不支持回滚，关系数据库支持
- **隔离性**：Redis事务串行执行，关系数据库有多种隔离级别
- **一致性**：都能保证一致性
- **持久性**：Redis取决于持久化配置

### Q2: 什么时候使用Lua脚本而不是事务？
**答案**：
- 需要复杂逻辑判断时
- 需要减少网络往返次数时
- 需要原子性执行复杂操作时
- 需要在服务端进行计算时

### Q3: Lua脚本有什么限制？
**答案**：
- 执行时间不能太长（默认5秒超时）
- 不能访问外部资源
- 不能使用随机函数（影响主从一致性）
- 脚本大小有限制

### Q4: 如何保证Lua脚本的幂等性？
**答案**：
- 使用条件判断
- 检查操作前的状态
- 使用唯一标识符
- 设计可重复执行的逻辑

### Q5: WATCH机制的实现原理？
**答案**：
- 每个key都有一个版本号
- WATCH命令记录key的当前版本
- 事务执行前检查版本是否变化
- 版本变化则取消事务执行

## 7. 最佳实践

### 事务使用建议
- 尽量减少事务中的命令数量
- 避免在事务中使用耗时命令
- 合理使用WATCH机制
- 处理事务执行失败的情况

### Lua脚本使用建议
- 保持脚本简洁高效
- 避免无限循环
- 使用EVALSHA提高性能
- 合理设计脚本参数

### 性能优化
- 预加载常用脚本
- 使用脚本缓存
- 减少脚本复杂度
- 监控脚本执行时间
