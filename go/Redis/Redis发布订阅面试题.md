# Redis发布订阅面试题

## 1. 什么是Redis发布订阅？

**定义**：
Redis发布订阅（Pub/Sub）是一种消息通信模式，发布者发送消息到频道，订阅者接收频道消息。

**核心概念**：
- **Publisher（发布者）**：发送消息的客户端
- **Subscriber（订阅者）**：接收消息的客户端
- **Channel（频道）**：消息传输的通道
- **Pattern（模式）**：支持通配符订阅

**特点**：
- **实时性**：消息实时推送
- **解耦性**：发布者和订阅者解耦
- **多对多**：支持多个发布者和订阅者
- **不持久化**：消息不存储，离线会丢失

## 2. 发布订阅的基本命令？

### 订阅操作
```redis
# 订阅单个频道
SUBSCRIBE channel1

# 订阅多个频道
SUBSCRIBE channel1 channel2 channel3

# 模式订阅（支持通配符）
PSUBSCRIBE news.*
PSUBSCRIBE user:*:notification

# 取消订阅
UNSUBSCRIBE channel1
PUNSUBSCRIBE news.*
```

### 发布操作
```redis
# 发布消息到频道
PUBLISH channel1 "Hello World"

# 发布JSON消息
PUBLISH user:1001:notification '{"type":"message","content":"You have a new message"}'

# 批量发布
PUBLISH news:tech "New AI breakthrough"
PUBLISH news:sports "Football match result"
```

### 查看订阅信息
```redis
# 查看活跃频道
PUBSUB CHANNELS [pattern]

# 查看频道订阅者数量
PUBSUB NUMSUB channel1 channel2

# 查看模式订阅数量
PUBSUB NUMPAT
```

## 3. 发布订阅的工作原理？

### 消息传递流程
1. **订阅者连接**：客户端连接Redis并订阅频道
2. **发布者发送**：发布者向频道发送消息
3. **Redis转发**：Redis将消息转发给所有订阅者
4. **订阅者接收**：订阅者实时接收消息

### 内部实现机制
- **频道字典**：Redis维护频道到订阅者的映射
- **模式链表**：存储模式订阅信息
- **消息推送**：使用推送模式，不是拉取模式
- **内存存储**：订阅关系存储在内存中

### 消息格式
```
订阅确认：["subscribe", "channel1", 1]
消息内容：["message", "channel1", "Hello World"]
模式消息：["pmessage", "news.*", "news.tech", "AI News"]
取消订阅：["unsubscribe", "channel1", 0]
```

## 4. 发布订阅的应用场景？

### 实时通知系统
```redis
# 用户通知
SUBSCRIBE user:1001:notifications
PUBLISH user:1001:notifications "You have a new message"

# 系统广播
SUBSCRIBE system:broadcast
PUBLISH system:broadcast "System maintenance at 2AM"

# 订单状态更新
SUBSCRIBE order:status:updates
PUBLISH order:status:updates '{"order_id":"12345","status":"shipped"}'
```

### 聊天系统
```redis
# 私聊
SUBSCRIBE chat:user1001:user1002
PUBLISH chat:user1001:user1002 "Hello, how are you?"

# 群聊
SUBSCRIBE chat:room:general
PUBLISH chat:room:general "Welcome to the general chat room"

# 在线状态
SUBSCRIBE user:online:status
PUBLISH user:online:status '{"user_id":"1001","status":"online"}'
```

### 缓存失效通知
```redis
# 缓存更新通知
SUBSCRIBE cache:invalidate
PUBLISH cache:invalidate "user:1001"

# 配置更新通知
SUBSCRIBE config:update
PUBLISH config:update '{"key":"max_connections","value":"1000"}'
```

### 微服务通信
```redis
# 服务发现
SUBSCRIBE service:discovery
PUBLISH service:discovery '{"service":"user-service","action":"register","host":"*************"}'

# 事件通知
SUBSCRIBE events:user:created
PUBLISH events:user:created '{"user_id":"1001","email":"<EMAIL>"}'
```

## 5. 发布订阅的限制？

### 消息可靠性
- **不持久化**：消息不存储，服务重启会丢失
- **离线丢失**：订阅者离线时会丢失消息
- **无确认机制**：不保证消息被成功接收
- **无重试机制**：发送失败不会重试

### 性能限制
- **内存消耗**：大量订阅者会消耗内存
- **网络带宽**：消息广播占用网络带宽
- **阻塞风险**：慢订阅者可能影响其他订阅者

### 功能限制
- **无消息队列**：不支持消息队列功能
- **无负载均衡**：所有订阅者都会收到消息
- **无消息过滤**：除了频道匹配，无其他过滤机制

## 6. 发布订阅 vs 消息队列？

### 对比分析
| 特性 | Pub/Sub | 消息队列(Stream/List) |
|------|---------|----------------------|
| 消息持久化 | 不支持 | 支持 |
| 离线消费 | 不支持 | 支持 |
| 消息确认 | 不支持 | 支持 |
| 负载均衡 | 不支持 | 支持 |
| 实时性 | 很好 | 较好 |
| 可靠性 | 较差 | 很好 |

### 选择建议
**选择Pub/Sub**：
- 实时性要求高
- 消息可以丢失
- 需要广播功能
- 系统解耦需求

**选择消息队列**：
- 消息不能丢失
- 需要离线消费
- 需要负载均衡
- 需要消息确认

## 7. 发布订阅的最佳实践？

### 频道设计
```redis
# 层次化命名
user:1001:notifications
order:status:updates
system:alerts:critical

# 使用模式订阅
PSUBSCRIBE user:*:notifications    # 订阅所有用户通知
PSUBSCRIBE order:*:updates         # 订阅所有订单更新
```

### 消息格式
```redis
# 使用JSON格式
PUBLISH user:notifications '{
  "type": "message",
  "user_id": "1001",
  "content": "You have a new message",
  "timestamp": "2023-12-01T10:00:00Z"
}'

# 包含元数据
PUBLISH events:user:login '{
  "event": "user_login",
  "user_id": "1001",
  "ip": "*************",
  "timestamp": "2023-12-01T10:00:00Z",
  "metadata": {"device": "mobile"}
}'
```

### 错误处理
```redis
# 订阅者异常处理
try:
    for message in pubsub.listen():
        process_message(message)
except Exception as e:
    logger.error(f"Message processing failed: {e}")
    # 重新订阅
    pubsub.subscribe(channels)
```

### 性能优化
```redis
# 连接池管理
pool = redis.ConnectionPool(host='localhost', port=6379, max_connections=20)
redis_client = redis.Redis(connection_pool=pool)

# 批量处理
messages = []
for message in pubsub.listen():
    messages.append(message)
    if len(messages) >= 100:
        process_batch(messages)
        messages = []
```

## 8. 发布订阅的监控？

### 关键指标
```redis
# 查看活跃频道数
PUBSUB CHANNELS

# 查看订阅者数量
PUBSUB NUMSUB channel1 channel2

# 查看模式订阅数量
PUBSUB NUMPAT

# 客户端连接信息
CLIENT LIST
```

### 监控脚本
```python
import redis

def monitor_pubsub():
    r = redis.Redis()
    
    # 监控频道数量
    channels = r.pubsub_channels()
    print(f"Active channels: {len(channels)}")
    
    # 监控订阅者数量
    for channel in channels:
        subscribers = r.pubsub_numsub(channel)
        print(f"Channel {channel}: {subscribers} subscribers")
    
    # 监控模式订阅
    pattern_count = r.pubsub_numpat()
    print(f"Pattern subscriptions: {pattern_count}")
```

## 9. 面试高频问题

### Q1: Redis发布订阅的优缺点？
**答案**：
**优点**：
- 实时性好，消息即时推送
- 发布者和订阅者解耦
- 支持一对多广播
- 支持模式匹配订阅

**缺点**：
- 消息不持久化，可能丢失
- 离线订阅者收不到消息
- 无消息确认机制
- 无负载均衡功能

### Q2: 如何保证发布订阅的消息可靠性？
**答案**：
1. **应用层确认**：在应用层实现消息确认
2. **消息持久化**：重要消息同时写入持久化存储
3. **重试机制**：发布者实现重试逻辑
4. **监控告警**：监控订阅者状态，及时发现问题

### Q3: 发布订阅适合什么场景？
**答案**：
**适合场景**：
- 实时通知系统
- 聊天系统
- 缓存失效通知
- 系统解耦

**不适合场景**：
- 需要消息持久化
- 需要消息确认
- 需要负载均衡
- 对可靠性要求高

### Q4: 如何处理慢订阅者问题？
**答案**：
1. **异步处理**：订阅者异步处理消息
2. **缓冲机制**：在订阅者端实现消息缓冲
3. **监控告警**：监控处理延迟，及时发现慢订阅者
4. **负载均衡**：使用多个订阅者实例

### Q5: 发布订阅与Stream的区别？
**答案**：
| 特性 | Pub/Sub | Stream |
|------|---------|--------|
| 消息持久化 | 否 | 是 |
| 消费者组 | 否 | 是 |
| 消息确认 | 否 | 是 |
| 实时性 | 很好 | 较好 |
| 可靠性 | 较差 | 很好 |

## 10. 发布订阅实战案例

### 实时日志系统
```python
# 日志发布者
import redis
import json

r = redis.Redis()

def publish_log(level, message, service):
    log_data = {
        'level': level,
        'message': message,
        'service': service,
        'timestamp': time.time()
    }
    r.publish(f'logs:{level}', json.dumps(log_data))
    r.publish(f'logs:{service}', json.dumps(log_data))

# 日志订阅者
pubsub = r.pubsub()
pubsub.psubscribe('logs:*')

for message in pubsub.listen():
    if message['type'] == 'pmessage':
        log_data = json.loads(message['data'])
        process_log(log_data)
```

### 微服务事件总线
```python
# 事件发布
def publish_event(event_type, data):
    event = {
        'type': event_type,
        'data': data,
        'timestamp': time.time(),
        'service': 'user-service'
    }
    r.publish(f'events:{event_type}', json.dumps(event))

# 事件订阅
pubsub = r.pubsub()
pubsub.subscribe('events:user_created', 'events:user_updated')

for message in pubsub.listen():
    if message['type'] == 'message':
        event = json.loads(message['data'])
        handle_event(event)
```
