# Redis Bitmap面试题

## 1. 什么是Redis Bitmap？

**定义**：
Bitmap（位图）是Redis基于String类型实现的位操作数据结构，每个位只能存储0或1。

**核心特点**：
- **内存高效**：每个位只占用1bit空间
- **快速操作**：位操作性能极高
- **支持位运算**：AND、OR、XOR、NOT等操作
- **适合布尔值存储**：用户签到、在线状态等

**底层实现**：
- 基于Redis String类型
- 最大支持2^32位（512MB）
- 自动扩容，按需分配内存

## 2. Bitmap的基本命令？

### 位操作命令
```redis
# 设置位值
SETBIT key offset value
SETBIT user:1001:signin 0 1    # 第1天签到
SETBIT user:1001:signin 1 1    # 第2天签到

# 获取位值
GETBIT key offset
GETBIT user:1001:signin 0      # 查看第1天是否签到

# 统计位为1的数量
BITCOUNT key [start end]
BITCOUNT user:1001:signin      # 统计总签到天数
BITCOUNT user:1001:signin 0 6  # 统计前7天签到天数
```

### 位运算命令
```redis
# 位运算操作
BITOP operation destkey key [key ...]

# AND运算（交集）
BITOP AND result user:1001:signin user:1002:signin

# OR运算（并集）
BITOP OR result user:1001:signin user:1002:signin

# XOR运算（异或）
BITOP XOR result user:1001:signin user:1002:signin

# NOT运算（取反）
BITOP NOT result user:1001:signin
```

### 位查找命令
```redis
# 查找第一个指定位值的位置
BITPOS key bit [start] [end]
BITPOS user:1001:signin 1      # 查找第一个签到的位置
BITPOS user:1001:signin 0      # 查找第一个未签到的位置
```

## 3. Bitmap的应用场景？

### 用户签到系统
```redis
# 用户签到
SETBIT user:1001:signin:202312 0 1    # 12月1日签到
SETBIT user:1001:signin:202312 1 1    # 12月2日签到

# 查看签到状态
GETBIT user:1001:signin:202312 0      # 查看12月1日是否签到

# 统计签到天数
BITCOUNT user:1001:signin:202312      # 12月总签到天数

# 查找连续签到
BITPOS user:1001:signin:202312 0 1    # 从第2位开始找第一个未签到
```

### 在线用户统计
```redis
# 记录在线用户
SETBIT online:20231201 1001 1         # 用户1001在线
SETBIT online:20231201 1002 1         # 用户1002在线

# 统计在线用户数
BITCOUNT online:20231201              # 当日在线用户数

# 查找特定用户状态
GETBIT online:20231201 1001           # 用户1001是否在线
```

### 用户权限管理
```redis
# 设置用户权限（位位置代表不同权限）
SETBIT user:1001:permissions 0 1     # 读权限
SETBIT user:1001:permissions 1 1     # 写权限
SETBIT user:1001:permissions 2 0     # 删除权限

# 检查权限
GETBIT user:1001:permissions 1       # 检查写权限

# 权限组合
BITOP AND user:1001:final_permissions user:1001:permissions role:admin:permissions
```

### 活跃用户分析
```redis
# 记录每日活跃用户
SETBIT active:20231201 1001 1
SETBIT active:20231202 1001 1
SETBIT active:20231202 1002 1

# 计算连续活跃用户（两天都活跃）
BITOP AND active:continuous active:20231201 active:20231202
BITCOUNT active:continuous

# 计算总活跃用户（任意一天活跃）
BITOP OR active:total active:20231201 active:20231202
BITCOUNT active:total
```

## 4. Bitmap性能优化？

### 内存优化
```redis
# 稀疏位图优化
# 对于稀疏数据，考虑使用Hash或Set
# 密度 < 1% 时，Hash可能更节省内存

# 位图压缩
# Redis会自动压缩连续的0
# 避免设置过大的offset造成内存浪费
```

### 操作优化
```redis
# 批量操作
# 使用Pipeline批量设置位
PIPELINE
SETBIT user:1001:signin 0 1
SETBIT user:1001:signin 1 1
SETBIT user:1001:signin 2 1
EXEC

# 范围操作
# 使用start和end参数限制操作范围
BITCOUNT user:1001:signin 0 30    # 只统计前31天
```

### 数据分片
```redis
# 按时间分片
SETBIT user:1001:signin:202312 day 1    # 按月分片
SETBIT user:1001:signin:20231201 hour 1  # 按日分片

# 按用户分片
SETBIT signin:202312:shard1 user_id 1   # 按用户ID分片
```

## 5. Bitmap与其他数据结构对比？

### 内存使用对比
| 数据结构 | 内存使用 | 适用场景 | 优缺点 |
|----------|----------|----------|--------|
| Bitmap | offset/8 bytes | 连续ID，布尔值 | 内存高效，但稀疏时浪费 |
| Set | 每元素约16字节 | 离散ID，需要元素值 | 灵活，但内存占用大 |
| Hash | 每字段约24字节 | 复杂数据 | 功能丰富，内存占用大 |
| HyperLogLog | 固定12KB | 基数统计 | 超低内存，但有误差 |

### 性能对比
```redis
# Bitmap方式
SETBIT user:signin 1001 1     # O(1)
GETBIT user:signin 1001       # O(1)
BITCOUNT user:signin          # O(n)

# Set方式
SADD user:signin 1001         # O(1)
SISMEMBER user:signin 1001    # O(1)
SCARD user:signin             # O(1)
```

## 6. Bitmap的限制和注意事项？

### 内存限制
- **最大长度**：2^32位（512MB）
- **稀疏问题**：稀疏位图会浪费内存
- **自动扩容**：设置大offset会自动扩容

### 性能考虑
- **BITCOUNT性能**：O(n)复杂度，大位图统计较慢
- **位运算性能**：多个大位图运算耗时较长
- **网络传输**：大位图传输占用带宽

### 使用建议
```redis
# 避免稀疏位图
# 用户ID不连续时，考虑使用Hash映射
HSET user:id_mapping user1001 0
HSET user:id_mapping user2005 1

# 分段存储
# 大位图分段存储，提高操作效率
SETBIT user:signin:part1 offset1 1    # 0-999999
SETBIT user:signin:part2 offset2 1    # 1000000-1999999
```

## 7. Bitmap实际应用案例？

### 电商用户行为分析
```redis
# 用户浏览商品
SETBIT product:1001:viewers 1001 1
SETBIT product:1001:viewers 1002 1

# 用户购买商品
SETBIT product:1001:buyers 1001 1

# 计算转化率
BITCOUNT product:1001:viewers    # 浏览用户数
BITCOUNT product:1001:buyers     # 购买用户数
# 转化率 = 购买用户数 / 浏览用户数

# 找出浏览但未购买的用户
BITOP XOR product:1001:not_buy product:1001:viewers product:1001:buyers
BITOP AND product:1001:not_buy product:1001:not_buy product:1001:viewers
```

### 网站访问统计
```redis
# 记录每日访问用户
SETBIT visit:20231201 user_id 1
SETBIT visit:20231202 user_id 1

# 统计日活跃用户
BITCOUNT visit:20231201

# 统计周活跃用户
BITOP OR visit:week1 visit:20231201 visit:20231202 ... visit:20231207
BITCOUNT visit:week1

# 统计新增用户（今天访问但昨天未访问）
BITOP NOT visit:20231130_not visit:20231130
BITOP AND visit:20231201_new visit:20231201 visit:20231130_not
BITCOUNT visit:20231201_new
```

### 实时推荐系统
```redis
# 用户兴趣标签
SETBIT user:1001:interests 0 1    # 科技
SETBIT user:1001:interests 1 1    # 体育
SETBIT user:1001:interests 2 0    # 娱乐

# 内容标签
SETBIT content:2001:tags 0 1      # 科技
SETBIT content:2001:tags 1 0      # 体育

# 计算匹配度
BITOP AND match:user1001:content2001 user:1001:interests content:2001:tags
BITCOUNT match:user1001:content2001  # 匹配的标签数
```

## 8. 面试高频问题

### Q1: Bitmap适合什么场景，不适合什么场景？
**答案**：
**适合场景**：
- 用户ID连续或可映射为连续整数
- 存储布尔值（是/否、有/无）
- 需要位运算（交集、并集等）
- 对内存使用敏感

**不适合场景**：
- 用户ID稀疏且不连续
- 需要存储复杂数据
- 需要频繁的随机访问
- 位图过于稀疏（密度<1%）

### Q2: 如何解决Bitmap的稀疏问题？
**答案**：
1. **ID映射**：将稀疏ID映射为连续ID
2. **分段存储**：按范围分段存储
3. **混合方案**：稀疏时用Set，密集时用Bitmap
4. **压缩存储**：使用压缩算法

### Q3: Bitmap的内存计算公式？
**答案**：
- **理论内存**：max_offset / 8 字节
- **实际内存**：Redis会对稀疏位图进行优化
- **示例**：offset为1000000的位图约需要125KB

### Q4: 如何优化Bitmap的BITCOUNT性能？
**答案**：
1. **限制范围**：使用start和end参数
2. **分段统计**：将大位图分段统计
3. **缓存结果**：缓存统计结果
4. **异步计算**：大位图统计异步化

### Q5: Bitmap如何实现复杂的用户分析？
**答案**：
```redis
# 活跃用户分析
BITOP AND active_both day1 day2           # 两天都活跃
BITOP OR active_either day1 day2          # 任意一天活跃
BITOP XOR active_diff day1 day2           # 只在一天活跃
BITOP AND active_only_day1 day1 active_diff  # 只在第一天活跃
```

## 9. Bitmap最佳实践

### 设计原则
- **ID连续性**：确保ID尽可能连续
- **合理分片**：按时间或业务分片
- **密度评估**：评估位图密度选择数据结构
- **内存监控**：监控位图内存使用

### 性能优化
- **批量操作**：使用Pipeline批量设置
- **范围限制**：操作时指定范围
- **结果缓存**：缓存计算结果
- **异步处理**：大位图操作异步化

### 运维建议
- **定期清理**：清理过期的位图数据
- **内存监控**：监控位图内存使用
- **性能测试**：定期测试位图操作性能
- **备份策略**：重要位图数据要备份
