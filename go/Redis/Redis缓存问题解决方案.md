# Redis缓存问题面试题

## 1. 缓存穿透问题

### 问题描述
查询不存在的数据，缓存和数据库都没有，导致每次请求都直接访问数据库。

### 典型场景
- 恶意攻击：故意查询不存在的用户ID
- 业务bug：查询已删除的商品信息
- 参数错误：传入非法的查询条件

### 解决方案

#### 1. 布隆过滤器（推荐）
- **原理**：在缓存前加布隆过滤器，快速判断数据是否可能存在
- **优点**：内存占用小，查询速度快
- **缺点**：存在误判率（假阳性），不支持删除
- **适用**：数据量大且相对稳定的场景

#### 2. 缓存空值
- **原理**：将空结果也缓存，设置较短过期时间（如5分钟）
- **优点**：实现简单，完全避免穿透
- **缺点**：占用缓存空间，可能缓存大量无效数据
- **适用**：查询模式相对固定的场景

#### 3. 参数校验
- **原理**：在接口层增加参数校验，过滤明显不合法的请求
- **优点**：从源头解决问题
- **缺点**：无法处理所有情况
- **适用**：作为第一道防线

## 2. 缓存击穿问题

### 问题描述
热点key过期瞬间，大量并发请求同时访问，导致请求直接打到数据库。

### 典型场景
- 热门商品详情页在促销活动期间
- 热门文章或视频的访问
- 系统配置信息的查询

### 解决方案

#### 1. 互斥锁（推荐）
- **原理**：只允许一个线程查询数据库，其他线程等待
- **实现**：使用`SETNX`实现分布式锁
- **优点**：保证只有一个请求访问数据库
- **缺点**：可能导致请求堆积

#### 2. 热点数据永不过期
- **原理**：设置逻辑过期时间，后台异步更新
- **优点**：用户请求永远不会等待
- **缺点**：可能返回过期数据，实现复杂

#### 3. 提前续期
- **原理**：监控热点key，在过期前自动续期
- **优点**：避免过期问题
- **缺点**：需要额外的监控机制

## 3. 缓存雪崩问题

### 问题描述
大量缓存在同一时间失效，导致大量请求直接访问数据库，可能导致数据库崩溃。

### 触发场景
- Redis服务器宕机或重启
- 大量key设置了相同的过期时间
- 网络故障导致缓存不可用

### 解决方案

#### 1. 过期时间随机化（推荐）
- **原理**：在基础过期时间上加随机值（如±10%）
- **优点**：简单有效，避免同时过期
- **实现**：`expire_time = base_time + random(0, base_time * 0.1)`

#### 2. 多级缓存架构
- **L1缓存**：本地缓存（如Caffeine）
- **L2缓存**：分布式缓存（Redis）
- **L3缓存**：数据库
- **优点**：提高系统容错能力

#### 3. 熔断降级
- **监控指标**：数据库连接数、响应时间、错误率
- **熔断策略**：超过阈值时返回默认值
- **恢复机制**：定期探测服务是否恢复

#### 4. 缓存预热
- **系统启动预热**：启动时预加载核心数据
- **定时预热**：定期刷新即将过期的热点数据
- **手动预热**：大促前手动预加载商品数据

## 4. 缓存更新策略

### 策略对比
| 策略 | 一致性 | 性能 | 复杂度 | 适用场景 |
|------|--------|------|--------|----------|
| Cache Aside | 弱一致 | 高 | 中 | 通用场景 |
| Read Through | 弱一致 | 中 | 低 | 读多写少 |
| Write Through | 强一致 | 低 | 中 | 强一致性要求 |
| Write Behind | 最终一致 | 高 | 高 | 高并发写入 |

### 最佳实践
1. **先更新数据库，再删除缓存**（推荐）
2. **延时双删策略**：删除缓存→更新数据库→延时删除缓存
3. **设置缓存过期时间兜底**
4. **监控缓存命中率和一致性**

### 面试要点
- 为什么不先删缓存再更新数据库？（并发时可能读到旧数据）
- 为什么不直接更新缓存？（并发更新可能导致数据不一致）

## 5. 缓存降级策略

### 降级触发条件
- 缓存服务不可用（宕机、网络故障）
- 缓存响应时间超过阈值
- 缓存错误率超过阈值

### 降级方案
1. **直接访问数据库**：适用于数据库能承受的场景，需要限流
2. **返回默认值**：适用于非核心数据，保证基本功能
3. **本地缓存兜底**：使用本地缓存，数据可能不是最新

### 实现要点
- 监控缓存健康状态
- 设置合理超时时间
- 快速失败机制
- 降级日志记录

## 6. 面试高频问题

### Q1: 三种缓存问题的本质区别？
**答案**：
- **穿透**：数据不存在，每次都查数据库
- **击穿**：热点数据过期，瞬间大量请求打到数据库
- **雪崩**：大量数据同时失效，数据库压力激增

### Q2: 布隆过滤器为什么会误判？
**答案**：多个不同的key可能映射到相同的位置，导致假阳性，但不会有假阴性。

### Q3: 为什么推荐先更新数据库再删缓存？
**答案**：
- 避免并发时读到脏数据
- 删除操作比更新操作更安全
- 即使删除失败，缓存也会过期

### Q4: 如何选择缓存更新策略？
**答案**：
- **读多写少**：Cache Aside
- **强一致性**：Write Through
- **高并发写**：Write Behind
- **简单场景**：Read Through

### Q5: 缓存预热的最佳时机？
**答案**：
- 系统启动时
- 业务低峰期
- 大促活动前
- 缓存失效前
