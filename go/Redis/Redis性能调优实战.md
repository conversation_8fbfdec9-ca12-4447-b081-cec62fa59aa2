# Redis性能调优实战指南

## 1. 性能监控指标

### 关键指标
- **QPS**：每秒查询数
- **延迟**：命令执行时间
- **内存使用率**：已用内存/总内存
- **命中率**：缓存命中次数/总请求次数
- **连接数**：当前客户端连接数
- **慢查询**：执行时间超过阈值的命令

### 监控命令
- `redis-cli --latency`：延迟监控
- `INFO stats`：统计信息
- `INFO memory`：内存信息
- `SLOWLOG GET`：慢查询日志

## 2. 内存优化

### 数据结构选择
- **小对象用Hash**：字段少时使用Hash存储对象
- **大对象用String**：字段多时序列化为JSON存储
- **计数用String**：INCR/DECR操作
- **去重用Set**：唯一性要求
- **排序用ZSet**：需要排序的场景

### 压缩配置优化
| 数据类型 | 配置项 | 默认值 | 说明 |
|---------|--------|--------|------|
| Hash | hash-max-ziplist-entries | 512 | 字段数阈值 |
| Hash | hash-max-ziplist-value | 64 | 单个值大小阈值 |
| List | list-max-ziplist-size | -2 | 压缩列表大小 |
| Set | set-max-intset-entries | 512 | 整数集合阈值 |
| ZSet | zset-max-ziplist-entries | 128 | 元素数阈值 |

### 内存淘汰策略
- **allkeys-lru**：推荐，所有key中淘汰最近最少使用
- **allkeys-lfu**：所有key中淘汰最少使用频率
- **volatile-lru**：过期key中淘汰最近最少使用
- **volatile-ttl**：淘汰即将过期的key
- **noeviction**：不淘汰，返回错误

### 内存优化技巧
- 设置合理的过期时间
- 避免大key（单个key超过10KB）
- 使用压缩算法
- 定期清理无用数据

## 3. 网络优化

### Pipeline批量操作
```python
# 优化前：逐个执行命令
import redis
r = redis.Redis()

for i in range(1000):
    r.set(f"key:{i}", f"value:{i}")

# 优化后：使用Pipeline
pipe = r.pipeline()
for i in range(1000):
    pipe.set(f"key:{i}", f"value:{i}")
pipe.execute()
```

### 连接池优化
```python
# 连接池配置
import redis

pool = redis.ConnectionPool(
    host='localhost',
    port=6379,
    max_connections=20,  # 最大连接数
    retry_on_timeout=True,
    socket_keepalive=True,
    socket_keepalive_options={}
)

r = redis.Redis(connection_pool=pool)
```

### 网络参数调优
```bash
# 系统级网络优化
# /etc/sysctl.conf
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000

# Redis配置
tcp-backlog 511
timeout 300
tcp-keepalive 300
```

## 4. 持久化优化

### RDB优化
```bash
# RDB配置优化
save 900 1      # 900秒内至少1个key变化
save 300 10     # 300秒内至少10个key变化
save 60 10000   # 60秒内至少10000个key变化

# 压缩配置
rdbcompression yes
rdbchecksum yes

# 后台保存
stop-writes-on-bgsave-error yes
```

### AOF优化
```bash
# AOF配置
appendonly yes
appendfilename "appendonly.aof"

# 同步策略
appendfsync everysec  # 推荐设置

# AOF重写
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 混合持久化（Redis 4.0+）
aof-use-rdb-preamble yes
```

## 5. 命令优化

### 避免大Key操作
```bash
# 问题：大List操作
LRANGE big_list 0 -1  # 可能返回百万条数据

# 优化：分页获取
LRANGE big_list 0 99    # 每次获取100条
LRANGE big_list 100 199
```

### 使用合适的数据结构
```bash
# 场景：统计网站UV
# 方案1：使用Set（内存占用大）
SADD uv:20231201 user1
SADD uv:20231201 user2
SCARD uv:20231201

# 方案2：使用HyperLogLog（内存占用小）
PFADD uv:20231201 user1
PFADD uv:20231201 user2
PFCOUNT uv:20231201
```

### 批量操作优化
```bash
# 优化前：多次单个操作
SET key1 value1
SET key2 value2
SET key3 value3

# 优化后：批量操作
MSET key1 value1 key2 value2 key3 value3

# Hash批量操作
HMSET user:1001 name "张三" age 25 city "北京"
```

## 6. 慢查询优化

### 慢查询配置
```bash
# 设置慢查询阈值（微秒）
slowlog-log-slower-than 10000

# 设置慢查询日志长度
slowlog-max-len 128

# 查看慢查询
SLOWLOG GET 10
SLOWLOG LEN
SLOWLOG RESET
```

### 常见慢查询优化
```bash
# 1. KEYS命令优化
# 问题：KEYS pattern（会阻塞Redis）
KEYS user:*

# 解决：使用SCAN
SCAN 0 MATCH user:* COUNT 100

# 2. 大集合操作优化
# 问题：获取大Set的所有成员
SMEMBERS big_set

# 解决：使用SSCAN分批获取
SSCAN big_set 0 COUNT 100

# 3. 排序操作优化
# 问题：SORT命令对大数据集排序
SORT big_list

# 解决：使用ZSet维护有序数据
ZADD sorted_data 1 item1
ZADD sorted_data 2 item2
ZRANGE sorted_data 0 -1
```

## 7. 集群优化

### Redis Cluster优化
```bash
# 集群配置优化
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 15000
cluster-require-full-coverage no

# 槽位迁移优化
cluster-migration-barrier 1
```

### 主从复制优化
```bash
# 主从配置优化
repl-diskless-sync yes
repl-diskless-sync-delay 5
repl-ping-slave-period 10
repl-timeout 60

# 从库配置
slave-read-only yes
slave-serve-stale-data yes
```

## 8. 客户端优化

### 连接管理
```python
# 连接复用
class RedisManager:
    def __init__(self):
        self.pool = redis.ConnectionPool(
            host='localhost',
            port=6379,
            max_connections=50,
            decode_responses=True
        )
        self.redis = redis.Redis(connection_pool=self.pool)
    
    def get_client(self):
        return self.redis

# 单例模式
redis_manager = RedisManager()
```

### 异步操作
```python
# 使用异步Redis客户端
import aioredis

async def async_redis_operations():
    redis = aioredis.from_url("redis://localhost")
    
    # 异步Pipeline
    pipe = redis.pipeline()
    for i in range(1000):
        pipe.set(f"key:{i}", f"value:{i}")
    await pipe.execute()
    
    await redis.close()
```

## 9. 监控和告警

### 关键监控指标
```bash
# 内存监控
used_memory
used_memory_peak
mem_fragmentation_ratio

# 性能监控
instantaneous_ops_per_sec
total_commands_processed
keyspace_hits
keyspace_misses

# 连接监控
connected_clients
blocked_clients
```

### 告警阈值设置
- **内存使用率** > 80%
- **命中率** < 95%
- **慢查询数量** > 100/分钟
- **连接数** > 最大连接数的80%
- **主从延迟** > 1秒

## 10. 性能测试

### 基准测试
```bash
# Redis自带基准测试
redis-benchmark -h localhost -p 6379 -c 100 -n 100000

# 指定测试命令
redis-benchmark -h localhost -p 6379 -t set,get -n 100000 -q

# 测试Pipeline
redis-benchmark -h localhost -p 6379 -n 100000 -P 16
```

### 自定义测试
```python
import time
import redis

def performance_test():
    r = redis.Redis()
    
    # 测试SET性能
    start_time = time.time()
    for i in range(10000):
        r.set(f"test:{i}", f"value:{i}")
    set_time = time.time() - start_time
    
    # 测试GET性能
    start_time = time.time()
    for i in range(10000):
        r.get(f"test:{i}")
    get_time = time.time() - start_time
    
    print(f"SET QPS: {10000/set_time:.2f}")
    print(f"GET QPS: {10000/get_time:.2f}")
```

## 11. 面试要点

### 性能优化方向
1. **内存优化**：数据结构选择、压缩配置
2. **网络优化**：Pipeline、连接池
3. **持久化优化**：RDB/AOF配置
4. **命令优化**：避免慢查询、批量操作

### 常见问题
- **Redis性能瓶颈在哪里**：网络IO、内存、CPU
- **如何提高Redis性能**：多维度优化
- **慢查询如何排查**：SLOWLOG命令
- **内存不足如何处理**：淘汰策略、数据清理

## 总结

Redis性能优化是一个系统工程，需要从多个维度进行：
1. **监控先行**：建立完善的监控体系
2. **数据结构优化**：选择合适的数据类型
3. **网络优化**：减少网络往返次数
4. **持久化优化**：平衡性能和数据安全
5. **运维优化**：合理配置和容量规划

通过系统性的优化，可以显著提升Redis的性能表现。
