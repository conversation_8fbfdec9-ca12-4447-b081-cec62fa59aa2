# Redis数据类型及底层结构面试题

## 1. Redis的5种基本数据类型及应用场景

### String（字符串）
- **应用场景**：缓存、计数器、分布式锁、session存储
- **底层结构**：SDS（Simple Dynamic String）
- **特点**：二进制安全、O(1)获取长度、最大512MB
- **面试要点**：为什么用SDS而不是C字符串？（长度获取O(1)、防止缓冲区溢出、二进制安全）

### Hash（哈希）
- **应用场景**：存储对象、用户信息、购物车
- **底层结构**：ZipList（小数据）→ HashTable（大数据）
- **转换阈值**：512个字段或64字节单个值
- **面试要点**：什么时候用Hash而不是String存储对象？（字段较多且需要单独操作时）

### List（列表）
- **应用场景**：消息队列、最新消息列表、栈和队列
- **底层结构**：QuickList（双向链表+压缩列表）
- **特点**：有序、可重复、支持两端操作
- **面试要点**：为什么不直接用链表？（内存碎片、缓存局部性）

### Set（集合）
- **应用场景**：标签、好友关系、去重、交并差集运算
- **底层结构**：IntSet（整数小集合）→ HashTable
- **面试要点**：什么时候用IntSet？（元素都是整数且数量少于512个）

### ZSet（有序集合）
- **应用场景**：排行榜、延时队列、范围查询
- **底层结构**：ZipList（小数据）→ SkipList+HashTable
- **面试要点**：为什么用跳表而不是红黑树？（实现简单、范围查询友好、并发性能好）

## 2. Redis为什么这么快？

1. **内存存储**：数据存储在内存中，避免磁盘IO
2. **单线程模型**：避免线程切换和锁竞争开销（6.0后网络IO多线程）
3. **IO多路复用**：epoll/kqueue高效处理网络连接
4. **高效数据结构**：针对不同场景优化的底层结构
5. **简单协议**：RESP协议解析效率高

## 3. Redis底层数据结构详解

### SDS（Simple Dynamic String）
- **用于**：String类型的底层实现
- **优势**：O(1)获取长度、二进制安全、动态扩容、预分配空间
- **面试要点**：相比C字符串的优势（防止缓冲区溢出、减少内存重分配次数）

### ZipList（压缩列表）
- **用于**：小数据量的Hash、List、ZSet
- **特点**：连续内存、节省空间
- **缺点**：连锁更新问题（最坏O(N²)）
- **面试要点**：什么是连锁更新？如何避免？

### HashTable（哈希表）
- **用于**：Hash、Set的底层实现
- **特点**：O(1)操作、渐进式rehash
- **扩容**：负载因子>1时触发
- **面试要点**：渐进式rehash如何实现？（分批迁移，避免阻塞）

### SkipList（跳表）
- **用于**：ZSet的底层实现
- **特点**：有序、O(logN)操作、支持范围查询
- **面试要点**：跳表的层数如何确定？（随机算法，平均logN层）

### IntSet（整数集合）
- **用于**：只包含整数的小Set
- **特点**：有序数组、二分查找、自动升级编码
- **面试要点**：编码升级过程（int16→int32→int64）

### QuickList（快速列表）
- **用于**：List的底层实现
- **结构**：双向链表+压缩列表
- **优势**：兼顾内存和性能，可配置压缩深度

## 4. 数据结构转换机制

### 转换阈值配置
| 数据类型 | 小结构 | 大结构 | 转换条件 |
|---------|--------|--------|----------|
| Hash | ZipList | HashTable | 元素>512个 或 单个值>64字节 |
| Set | IntSet | HashTable | 元素>512个 或 非整数元素 |
| ZSet | ZipList | SkipList+Hash | 元素>128个 或 单个值>64字节 |

**面试要点**：转换是不可逆的，只能从小结构转为大结构。

## 5. 高频面试问题

### Q1: ZSet为什么用跳表而不是红黑树？
**答案**：
1. **实现简单**：跳表实现比红黑树简单得多
2. **范围查询**：跳表天然支持ZRANGE等范围操作
3. **内存局部性**：跳表内存访问模式更友好
4. **并发性能**：跳表更适合读多写少的并发场景

### Q2: Redis6.0引入多线程了吗？
**答案**：
- **网络IO多线程**：读写socket操作使用多线程
- **命令执行单线程**：保证数据操作的原子性
- **目的**：提高网络IO处理能力，不改变数据安全性

### Q3: SDS相比C字符串有什么优势？
**答案**：
1. **O(1)获取长度**：SDS记录字符串长度
2. **防止缓冲区溢出**：API会检查空间是否足够
3. **减少内存重分配**：空间预分配和惰性空间释放
4. **二进制安全**：可以存储任意二进制数据

### Q4: 什么情况下会发生数据结构转换？
**答案**：
1. **元素数量超限**：超过配置的最大元素数
2. **元素大小超限**：单个元素超过配置的最大字节数
3. **数据类型变化**：如Set中加入非整数元素
4. **转换不可逆**：只能从紧凑结构转为复杂结构

### Q5: Redis的内存布局是怎样的？
**答案**：
- **数据存储**：键值对数据
- **过期字典**：存储键的过期时间
- **复制积压缓冲区**：主从复制使用
- **AOF缓冲区**：AOF持久化使用
- **客户端缓冲区**：客户端输入输出缓冲