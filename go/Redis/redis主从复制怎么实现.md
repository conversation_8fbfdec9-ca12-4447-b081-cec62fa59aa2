# Redis主从复制面试题

## 1. Redis主从复制原理

### 复制类型
Redis主从复制分为**全量同步**和**增量同步**两种方式。

### 全量同步
**触发条件**：
- 从库第一次连接主库
- 复制偏移量不在主库的复制积压缓冲区内
- 主库重启导致runid变化

**同步流程**：
1. **PSYNC命令**：从库发送PSYNC runid offset
2. **BGSAVE生成RDB**：主库后台生成快照，记录后续命令到缓冲区
3. **传输RDB文件**：主库发送RDB文件给从库
4. **加载RDB数据**：从库清空数据并加载RDB
5. **同步缓冲区命令**：发送RDB生成期间的增量命令
6. **进入增量同步**：后续命令实时同步

### 增量同步
**工作原理**：
- 主库执行写命令后，异步发送给所有从库
- 从库接收并执行相同的写命令
- 通过复制偏移量跟踪同步进度

**面试要点**：主从复制是异步的，可能存在数据延迟。

## 2. 主从复制关键机制

### 复制偏移量（Replication Offset）
- **主库偏移量**：记录已发送给从库的数据量
- **从库偏移量**：记录已接收并处理的数据量
- **作用**：判断主从数据是否一致，支持断点续传

### 复制积压缓冲区（Replication Backlog）
- **大小**：默认1MB，可配置repl-backlog-size
- **作用**：保存最近的写命令，支持增量同步
- **原理**：环形缓冲区，FIFO机制
- **面试要点**：缓冲区太小会导致频繁全量同步

### 服务器运行ID（Run ID）
- **生成**：Redis启动时生成40位随机字符串
- **作用**：标识Redis实例，重启后会变化
- **用途**：从库通过runid判断主库是否重启

## 3. 主从复制优缺点

### 优点
- **读写分离**：主库写，从库读，提高并发能力
- **数据备份**：从库作为数据备份
- **故障恢复**：主库故障时可切换到从库
- **负载分担**：多个从库分担读请求

### 缺点
- **数据延迟**：异步复制导致主从数据不一致
- **写能力限制**：只有主库能写，无法水平扩展写能力
- **故障切换复杂**：需要手动或Sentinel自动切换
- **全量同步开销大**：网络和磁盘IO压力

## 4. 面试高频问题

### Q1: 主从复制是同步还是异步？
**答案**：异步复制。主库执行写命令后立即返回客户端，然后异步发送给从库。

### Q2: 从库可以有从库吗？
**答案**：可以。支持链式复制（A→B→C），但会增加复制延迟，一般不超过3层。

### Q3: 主库宕机后如何恢复？
**答案**：
1. **手动切换**：修改应用配置，将从库提升为主库
2. **自动切换**：使用Redis Sentinel实现自动故障转移

### Q4: 如何减少主从复制延迟？
**答案**：
- 优化网络环境（带宽、延迟）
- 增加复制积压缓冲区大小
- 避免主库执行耗时操作（如KEYS *）
- 使用SSD提高磁盘IO性能

### Q5: 什么情况下会触发全量同步？
**答案**：
- 从库第一次连接主库
- 主库重启导致runid变化
- 复制偏移量不在主库的复制积压缓冲区内

### Q6: 如何监控主从复制状态？
**答案**：
- 使用INFO replication命令查看复制信息
- 监控复制偏移量差值
- 监控复制延迟时间
- 设置复制超时告警
