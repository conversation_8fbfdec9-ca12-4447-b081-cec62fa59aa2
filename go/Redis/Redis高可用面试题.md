# Redis高可用面试题

## 1. Redis主从复制原理？

**复制过程**：

**全量复制（第一次同步）**：
1. 从库发送PSYNC命令到主库
2. 主库执行BGSAVE生成RDB快照
3. 主库将RDB文件发送给从库
4. 从库清空数据并加载RDB文件
5. 主库将复制期间的写命令发送给从库

**增量复制（日常同步）**：
1. 主库将写命令实时发送给从库
2. 从库执行命令保持数据同步
3. 使用复制偏移量跟踪同步进度

**断线重连**：
- 从库记录复制偏移量
- 重连后发送PSYNC offset命令
- 主库根据偏移量决定全量或增量复制

## 2. 主从复制的配置和优化？

**基本配置**：
```
# 从库配置
replicaof ************* 6379
replica-read-only yes

# 主库配置
bind 0.0.0.0
protected-mode no
```

**复制优化**：
```
# 复制缓冲区大小
repl-backlog-size 1mb
# 缓冲区保留时间
repl-backlog-ttl 3600
# 无磁盘复制
repl-diskless-sync yes
# 复制超时时间
repl-timeout 60
```

**监控指标**：
- 复制偏移量差异
- 复制延迟时间
- 主从连接状态

## 3. Redis Sentinel哨兵机制？

**主要功能**：
- **监控**：监控主从服务器运行状态
- **通知**：故障时通知管理员或应用程序
- **故障转移**：自动进行主从切换
- **配置提供**：为客户端提供服务发现

**工作原理**：
1. 多个Sentinel监控同一个主库
2. 通过心跳检测判断服务器状态
3. 主观下线：单个Sentinel认为服务器下线
4. 客观下线：多数Sentinel确认服务器下线
5. 选举领导者Sentinel执行故障转移

**故障转移流程**：
1. 检测主库客观下线
2. Sentinel之间选举领导者
3. 从从库中选择新的主库
4. 让其他从库复制新主库
5. 更新配置并通知客户端

## 4. Sentinel的配置和部署？

**Sentinel配置文件**：
```
# 监控主库
sentinel monitor mymaster ************* 6379 2
# 主观下线时间
sentinel down-after-milliseconds mymaster 5000
# 故障转移超时时间
sentinel failover-timeout mymaster 60000
# 并行复制数量
sentinel parallel-syncs mymaster 1
```

**部署建议**：
- 至少部署3个Sentinel实例
- Sentinel部署在不同机器上
- 奇数个Sentinel避免脑裂
- 合理设置quorum值

**客户端连接**：
- 连接Sentinel而不是直接连接Redis
- 通过Sentinel获取主库地址
- 监听主库切换事件

## 5. Redis Cluster集群？

**核心特性**：
- **数据分片**：16384个哈希槽分布式存储
- **无中心架构**：每个节点都是对等的
- **自动故障转移**：主节点故障时自动切换
- **在线扩容**：支持动态添加删除节点

**数据分布算法**：
```
slot = CRC16(key) % 16384
```

**集群通信**：
- **Gossip协议**：节点间交换状态信息
- **心跳检测**：定期检查节点健康状态
- **配置传播**：集群配置变更的传播

**槽位管理**：
- 每个主节点负责一部分槽位
- 槽位信息存储在每个节点
- 客户端缓存槽位映射关系

## 6. Cluster的搭建和管理？

**集群搭建**：
```bash
# 创建集群
redis-cli --cluster create \
  *************:7000 *************:7001 \
  *************:7002 *************:7003 \
  *************:7004 *************:7005 \
  --cluster-replicas 1
```

**节点配置**：
```
# 开启集群模式
cluster-enabled yes
# 集群配置文件
cluster-config-file nodes-6379.conf
# 节点超时时间
cluster-node-timeout 15000
```

**集群管理命令**：
- CLUSTER NODES：查看集群节点信息
- CLUSTER INFO：查看集群状态
- CLUSTER SLOTS：查看槽位分配
- CLUSTER FAILOVER：手动故障转移

## 7. 集群扩容和缩容？

**扩容流程**：
1. 启动新节点
2. 将新节点加入集群
3. 重新分配槽位
4. 迁移数据到新节点

**缩容流程**：
1. 将要删除节点的槽位迁移到其他节点
2. 从集群中移除节点
3. 关闭节点服务

**槽位迁移**：
```bash
# 重新分片
redis-cli --cluster reshard *************:7000
# 指定迁移的槽位数量和目标节点
```

## 8. 集群的限制和注意事项？

**功能限制**：
- 不支持多key操作（除非key在同一槽位）
- 不支持多数据库（只能使用db0）
- 复制结构只支持一层（主-从）

**性能考虑**：
- 跨槽位操作需要多次网络请求
- 大量小key可能导致槽位分布不均
- 网络分区可能影响集群可用性

**运维注意**：
- 定期检查槽位分布
- 监控节点间网络延迟
- 及时处理故障节点

## 9. 高可用方案选择？

**主从复制**：
- **适用场景**：读多写少，对一致性要求不高
- **优点**：简单易用，读写分离
- **缺点**：主库单点故障，手动切换

**Sentinel**：
- **适用场景**：需要自动故障转移，数据量不大
- **优点**：自动故障转移，配置简单
- **缺点**：不支持数据分片，扩展性有限

**Cluster**：
- **适用场景**：大数据量，需要水平扩展
- **优点**：数据分片，自动故障转移，可扩展
- **缺点**：配置复杂，功能限制较多

## 10. 高可用监控和运维？

**监控指标**：
- 节点存活状态
- 主从延迟时间
- 集群槽位分布
- 故障转移次数

**告警设置**：
- 主库不可用
- 主从延迟过大
- 集群节点下线
- 槽位分布不均

**运维工具**：
- redis-cli：基本管理命令
- redis-sentinel：哨兵管理
- redis-trib：集群管理（旧版本）
- 第三方监控工具：RedisInsight、Grafana等

**故障处理**：
- 主库故障：Sentinel自动切换或手动切换
- 从库故障：重启或重新搭建
- 网络分区：检查网络连接，必要时手动干预
- 数据不一致：重新进行全量复制
