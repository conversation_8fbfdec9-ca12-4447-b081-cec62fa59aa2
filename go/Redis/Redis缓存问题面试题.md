# Redis缓存问题面试题

## 1. 缓存穿透、击穿、雪崩？

### 缓存穿透

**问题描述**：
- 查询不存在的数据，缓存和数据库都没有
- 每次请求都会到达数据库
- 可能被恶意攻击利用

**解决方案**：
1. **布隆过滤器**：
   - 在缓存前加一层布隆过滤器
   - 快速判断数据是否可能存在
   - 存在误判但不会漏判

2. **缓存空值**：
   - 将空结果也缓存起来
   - 设置较短的过期时间
   - 避免频繁查询数据库

3. **参数校验**：
   - 在接口层做参数合法性校验
   - 过滤明显不合法的请求

### 缓存击穿

**问题描述**：
- 热点key在某个时间点过期
- 大量并发请求同时访问这个key
- 瞬间压垮数据库

**解决方案**：
1. **互斥锁**：
   - 只允许一个线程查询数据库
   - 其他线程等待或返回旧数据
   - 保证数据库不被击穿

2. **热点数据永不过期**：
   - 设置热点数据永不过期
   - 通过异步任务更新数据
   - 保证缓存始终有效

3. **预热机制**：
   - 在业务高峰前预热热点数据
   - 延长热点数据的过期时间

### 缓存雪崩

**问题描述**：
- 大量缓存在同一时间过期
- 或者缓存服务器宕机
- 导致数据库压力激增

**解决方案**：
1. **过期时间随机化**：
   - 在基础过期时间上加随机值
   - 避免大量key同时过期

2. **多级缓存**：
   - 本地缓存 + 分布式缓存
   - 提高缓存的可用性

3. **限流降级**：
   - 在数据库前加限流机制
   - 超过阈值时返回默认值

4. **集群部署**：
   - Redis集群避免单点故障
   - 提高系统可用性

## 2. 缓存一致性问题？

### 常见不一致场景

**更新数据库成功，更新缓存失败**：
- 数据库有新数据，缓存是旧数据
- 读取时会获取到过期数据

**更新缓存成功，更新数据库失败**：
- 缓存有新数据，数据库是旧数据
- 缓存过期后读取到旧数据

**并发更新导致的不一致**：
- 多个线程同时更新同一数据
- 可能导致数据版本混乱

### 解决方案

**1. Cache Aside模式**：
- 读：先读缓存，缓存没有再读数据库，然后更新缓存
- 写：先更新数据库，然后删除缓存

**2. 延时双删策略**：
```
1. 删除缓存
2. 更新数据库  
3. 延时N秒后再删除缓存
```

**3. 基于消息队列的异步更新**：
- 数据库更新后发送消息
- 消费者异步更新缓存
- 保证最终一致性

**4. 使用分布式锁**：
- 更新时加分布式锁
- 保证同一时间只有一个线程更新
- 避免并发问题

## 3. 热点数据问题？

### 问题表现

**访问倾斜**：
- 少数key承担大部分访问量
- 可能导致单个Redis节点过载

**缓存击穿风险**：
- 热点key过期时影响巨大
- 需要特殊处理

### 解决方案

**1. 热点数据识别**：
- 监控key的访问频率
- 使用LFU算法识别热点
- 实时统计访问模式

**2. 热点数据预热**：
- 提前加载热点数据到缓存
- 设置合理的过期时间
- 定期刷新热点数据

**3. 多级缓存**：
- 本地缓存存储超热点数据
- 减少对Redis的访问压力
- 提高响应速度

**4. 读写分离**：
- 热点数据使用多个从库
- 分散读取压力
- 提高并发能力

## 4. 大key问题？

### 问题影响

**内存占用**：
- 单个key占用大量内存
- 影响其他数据的存储

**操作耗时**：
- 大key的操作耗时长
- 可能阻塞其他命令

**网络传输**：
- 大key传输占用大量带宽
- 影响网络性能

### 解决方案

**1. 大key拆分**：
- 将大key拆分为多个小key
- 使用Hash结构存储对象
- 分批次操作数据

**2. 压缩存储**：
- 对value进行压缩
- 减少内存占用
- 注意压缩解压的CPU开销

**3. 异步删除**：
- 使用UNLINK命令异步删除
- 避免阻塞主线程
- 提高系统响应性

**4. 监控告警**：
- 定期扫描大key
- 设置大key告警
- 及时处理大key问题

## 5. 缓存预热策略？

### 预热时机

**系统启动时**：
- 应用启动时预加载热点数据
- 避免冷启动时的性能问题

**业务高峰前**：
- 在流量高峰前预热数据
- 保证高峰期的服务质量

**定期预热**：
- 定期刷新可能过期的热点数据
- 保持缓存的有效性

### 预热方法

**1. 全量预热**：
- 将所有数据加载到缓存
- 适用于数据量不大的场景
- 保证缓存命中率

**2. 热点预热**：
- 只预热访问频率高的数据
- 根据历史访问记录确定热点
- 节省缓存空间

**3. 渐进式预热**：
- 逐步加载数据到缓存
- 避免瞬间压力过大
- 平滑过渡到正常状态

## 6. 缓存更新策略？

### 更新模式

**1. Cache Aside（旁路缓存）**：
- 应用程序管理缓存
- 读：缓存 -> 数据库 -> 更新缓存
- 写：更新数据库 -> 删除缓存

**2. Read Through（读穿透）**：
- 缓存代理读操作
- 缓存未命中时自动加载数据
- 对应用透明

**3. Write Through（写穿透）**：
- 缓存代理写操作
- 同时更新缓存和数据库
- 保证强一致性

**4. Write Behind（写回）**：
- 只更新缓存，异步写数据库
- 提高写性能
- 可能丢失数据

### 选择建议

**对一致性要求高**：选择Write Through
**对性能要求高**：选择Write Behind
**平衡性能和一致性**：选择Cache Aside

## 7. 缓存监控和运维？

### 关键指标

**命中率指标**：
- 缓存命中率
- 缓存穿透率
- 缓存更新频率

**性能指标**：
- 响应时间
- QPS/TPS
- 错误率

**资源指标**：
- 内存使用率
- CPU使用率
- 网络带宽

### 告警设置

**命中率告警**：
- 命中率低于阈值（如95%）
- 穿透率高于阈值（如5%）

**性能告警**：
- 响应时间超过阈值
- 错误率超过阈值

**资源告警**：
- 内存使用率过高
- 连接数过多

### 故障处理

**缓存不可用**：
- 降级到数据库
- 启用本地缓存
- 快速恢复缓存服务

**数据不一致**：
- 清理缓存重新加载
- 检查更新逻辑
- 修复数据一致性

**性能问题**：
- 分析慢查询
- 优化缓存策略
- 扩容缓存资源
