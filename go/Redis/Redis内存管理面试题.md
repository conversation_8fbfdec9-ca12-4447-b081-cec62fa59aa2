# Redis内存管理面试题

## 1. Redis内存淘汰策略？

**8种淘汰策略**：

**针对所有key**：
1. **noeviction**：不淘汰任何key，内存满时返回错误
2. **allkeys-lru**：在所有key中淘汰最近最少使用的key
3. **allkeys-lfu**：在所有key中淘汰使用频率最低的key
4. **allkeys-random**：在所有key中随机淘汰

**针对设置过期时间的key**：
5. **volatile-lru**：在过期key中淘汰最近最少使用的key
6. **volatile-lfu**：在过期key中淘汰使用频率最低的key
7. **volatile-random**：在过期key中随机淘汰
8. **volatile-ttl**：淘汰即将过期的key（TTL最小）

**选择建议**：
- 一般推荐使用 `allkeys-lru`
- 如果访问模式均匀，可选择 `allkeys-random`
- 如果有明确的热点数据，选择 `allkeys-lfu`

## 2. LRU和LFU算法的实现？

**LRU（Least Recently Used）**：
- **原理**：淘汰最近最少使用的数据
- **Redis实现**：近似LRU算法，使用采样方式
- **优点**：实现简单，适合大多数场景
- **缺点**：可能淘汰偶尔访问的热点数据

**LFU（Least Frequently Used）**：
- **原理**：淘汰使用频率最低的数据
- **Redis实现**：使用计数器记录访问频率
- **优点**：更准确识别热点数据
- **缺点**：实现复杂，内存开销大

**近似算法优势**：
- 节省内存开销
- 提高执行效率
- 在大多数场景下效果接近精确算法

## 3. Redis过期删除策略？

**三种策略**：

**定时删除**：
- 为每个key设置定时器
- 到期立即删除
- 优点：及时释放内存
- 缺点：CPU开销大

**惰性删除**：
- 访问key时检查是否过期
- 过期则删除并返回nil
- 优点：CPU开销小
- 缺点：内存占用大

**定期删除**：
- 定期随机检查部分key
- 删除发现的过期key
- 优点：平衡CPU和内存开销
- 缺点：可能有过期key残留

**Redis采用**：惰性删除 + 定期删除的组合策略

## 4. Redis内存使用优化？

**数据结构优化**：
- 选择合适的数据类型
- 利用压缩列表等紧凑结构
- 避免存储过大的value

**key设计优化**：
- 使用短而有意义的key名
- 避免使用过长的key
- 合理设计key的层次结构

**过期时间设置**：
- 为临时数据设置合理的过期时间
- 避免大量key同时过期
- 使用随机过期时间

**内存监控**：
- 定期检查内存使用情况
- 监控大key的存在
- 分析内存碎片率

## 5. Redis内存碎片问题？

**产生原因**：
- 频繁的内存分配和释放
- 不同大小的内存块分配
- 操作系统的内存管理策略

**影响**：
- 实际内存使用超过数据大小
- 可能导致内存不足
- 影响系统性能

**解决方案**：
- 重启Redis实例（简单粗暴）
- 使用MEMORY PURGE命令（Redis 4.0+）
- 调整内存分配器参数
- 定期进行内存整理

**预防措施**：
- 避免频繁的大key操作
- 合理设计数据结构
- 定期监控内存碎片率

## 6. Redis大key问题？

**大key定义**：
- String类型：value超过10KB
- Hash、List、Set、ZSet：元素个数超过5000

**危害**：
- 占用大量内存
- 操作耗时长，阻塞其他命令
- 网络传输开销大
- 主从复制延迟

**发现方法**：
- 使用redis-cli --bigkeys
- 使用MEMORY USAGE命令
- 监控慢查询日志
- 自定义脚本扫描

**解决方案**：
- 拆分大key为多个小key
- 使用Hash结构存储对象
- 设置合理的过期时间
- 异步删除大key（UNLINK命令）

## 7. Redis内存监控指标？

**核心指标**：
- **used_memory**：Redis使用的内存总量
- **used_memory_rss**：操作系统分配给Redis的内存
- **used_memory_peak**：历史内存使用峰值
- **mem_fragmentation_ratio**：内存碎片率

**计算公式**：
```
内存碎片率 = used_memory_rss / used_memory
```

**告警阈值**：
- 内存使用率 > 80%
- 内存碎片率 > 1.5 或 < 1.0
- 内存增长速度异常

**监控命令**：
- INFO memory：查看内存相关信息
- MEMORY STATS：详细内存统计
- MEMORY USAGE key：查看key的内存使用

## 8. Redis内存配置优化？

**maxmemory配置**：
```
# 设置最大内存限制
maxmemory 2gb

# 设置内存淘汰策略
maxmemory-policy allkeys-lru

# 设置淘汰样本数量
maxmemory-samples 5
```

**其他内存相关配置**：
```
# Hash类型优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List类型优化  
list-max-ziplist-size -2
list-compress-depth 0

# Set类型优化
set-max-intset-entries 512

# ZSet类型优化
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
```

## 9. 内存泄漏排查？

**常见原因**：
- 没有设置过期时间的key
- 大key占用过多内存
- 客户端连接泄漏
- 慢查询导致内存积压

**排查步骤**：
1. 检查内存使用趋势
2. 分析大key分布
3. 检查过期key比例
4. 监控客户端连接数
5. 分析慢查询日志

**排查工具**：
- redis-cli --bigkeys
- redis-cli --memkeys
- MEMORY DOCTOR命令
- 自定义监控脚本

## 10. 内存优化最佳实践？

**设计原则**：
- 选择合适的数据结构
- 控制key和value的大小
- 合理设置过期时间
- 避免内存碎片

**运维建议**：
- 定期监控内存使用
- 及时清理无用数据
- 合理配置内存淘汰策略
- 预留足够的内存空间

**应急处理**：
- 临时调整maxmemory
- 手动删除部分数据
- 重启Redis实例
- 扩容内存资源
