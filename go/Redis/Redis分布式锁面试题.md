# Redis分布式锁面试题

## 1. 什么是分布式锁？为什么需要分布式锁？

### 分布式锁定义
**分布式锁**：在分布式系统中，用于控制多个进程或线程对共享资源的访问，确保同一时刻只有一个进程能够访问临界资源。

### 使用场景
1. **防止重复执行**：定时任务、消息消费
2. **库存扣减**：电商秒杀、库存管理
3. **数据一致性**：分布式事务、缓存更新
4. **资源竞争**：文件操作、配置更新

## 2. Redis分布式锁的基本实现

### 核心命令
- **加锁**：`SET lock_key unique_value PX 30000 NX`
- **解锁**：Lua脚本保证原子性判断和删除

### 关键参数
- **lock_key**：锁的唯一标识
- **unique_value**：客户端唯一标识（防止误删）
- **PX 30000**：过期时间30秒（防止死锁）
- **NX**：只有key不存在时才设置

### 解锁Lua脚本
```lua
if redis.call("get", KEYS[1]) == ARGV[1] then
    return redis.call("del", KEYS[1])
else
    return 0
end
```

## 3. 分布式锁的核心问题

### 问题1：锁超时
**问题**：业务执行时间超过锁的过期时间

**解决方案**：
1. **合理设置过期时间**：根据业务执行时间评估
2. **锁续期机制**：定时刷新锁的过期时间
3. **看门狗机制**：Redisson等客户端提供的自动续期

### 问题2：误删锁
**问题**：客户端A的锁过期后，客户端B获得锁，客户端A执行完删除了客户端B的锁

**解决方案**：
- 使用唯一标识符（UUID、线程ID等）
- 解锁时先判断是否是自己的锁
- 使用Lua脚本保证判断和删除的原子性

### 问题3：死锁
**问题**：客户端获取锁后崩溃，锁无法释放

**解决方案**：
- 设置合理的过期时间
- 使用TTL自动释放锁
- 监控锁的持有时间

## 4. Redlock算法

### 算法原理
Redis官方提出的分布式锁算法，解决单点故障问题。

### 核心步骤
1. **向N个独立Redis实例申请锁**（通常N=5）
2. **判断成功条件**：
   - 超过半数实例获取成功
   - 总耗时小于锁的有效时间
3. **失败时释放所有已获取的锁**

### 优缺点对比
| 方面 | 优点 | 缺点 |
|------|------|------|
| 可靠性 | 解决单点故障 | 实现复杂 |
| 性能 | 提高容错能力 | 网络开销大 |
| 一致性 | 多数派保证 | 时钟同步要求高 |

**面试要点**：Redlock在网络分区时可能出现安全性问题，实际使用需谨慎。

## 5. 分布式锁的最佳实践

### 设计原则
1. **互斥性**：同一时刻只有一个客户端能获取锁
2. **安全性**：只有持有锁的客户端才能释放锁
3. **活性**：不会出现死锁，锁最终会被释放
4. **容错性**：部分节点故障不影响锁服务

### 实现要点
- 使用SET命令的NX和PX参数
- 使用唯一标识符防止误删
- 使用Lua脚本保证原子性
- 设置合理的过期时间
- 实现锁续期机制（看门狗）

### 性能优化建议
- 减少网络往返：使用Lua脚本
- 合理设置超时时间：平衡安全性和性能
- 使用连接池：减少连接开销
- 监控锁的使用情况：及时发现问题

## 6. 分布式锁方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| Redis | 性能高、实现简单 | 可能丢失锁 | 性能要求高 |
| ZooKeeper | 强一致性、自动释放 | 性能一般、复杂 | 强一致性要求 |
| 数据库 | 事务支持、强一致 | 性能差、单点 | 简单场景 |
| Etcd | 强一致性、自动续期 | 学习成本高 | 云原生环境 |

**面试要点**：根据业务需求选择合适的方案，没有银弹。

## 7. 面试高频问题

### Q1: Redis分布式锁的实现原理？
**答案**：使用`SET key value PX milliseconds NX`实现互斥和过期，用Lua脚本保证解锁原子性。

### Q2: 如何防止锁超时？
**答案**：
1. 合理评估业务执行时间
2. 实现锁续期机制（看门狗）
3. 使用Redisson等客户端的自动续期功能

### Q3: 如何防止误删锁？
**答案**：使用唯一标识符（UUID+线程ID），解锁时先判断是否是自己的锁。

### Q4: Redlock算法适用场景？
**答案**：对锁的可靠性要求极高的场景，但要注意网络分区时的安全性问题。

### Q5: 分布式锁的性能优化？
**答案**：
- 减小锁粒度
- 缩短持有时间
- 使用连接池
- 合理设置重试策略

## 8. 实际应用要点

### 关键注意事项
1. **幂等性设计**：确保重复执行安全
2. **异常处理**：finally块中释放锁
3. **监控告警**：监控锁的成功率和持有时间
4. **降级方案**：锁服务不可用时的备选策略

### 常见陷阱
- 忘记设置过期时间导致死锁
- 没有唯一标识导致误删锁
- 锁粒度过大影响并发性能
- 没有考虑时钟偏移问题
