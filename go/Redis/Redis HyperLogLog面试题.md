# Redis HyperLogLog面试题

## 1. 什么是HyperLogLog？

**定义**：
HyperLogLog是一种概率性数据结构，用于估算集合的基数（不重复元素的数量），具有极低的内存消耗。

**核心特点**：
- **内存效率**：固定12KB内存，可统计2^64个元素
- **概率算法**：基于概率统计，存在误差（标准误差0.81%）
- **不可逆**：只能统计基数，无法获取具体元素
- **合并性**：多个HyperLogLog可以合并

**应用场景**：
- 网站UV统计
- 用户去重计数
- 大数据基数估算

## 2. HyperLogLog的基本命令？

### 基础操作
```redis
# 添加元素
PFADD uv:20231201 user1 user2 user3

# 获取基数估算值
PFCOUNT uv:20231201

# 合并多个HyperLogLog
PFMERGE uv:total uv:20231201 uv:20231202 uv:20231203
```

### 批量操作
```redis
# 批量添加
PFADD page:home:uv user1 user2 user3 user4 user5

# 多个HLL的基数统计
PFCOUNT uv:20231201 uv:20231202 uv:20231203

# 检查添加是否改变了基数
PFADD uv:20231201 user1  # 返回0（已存在）
PFADD uv:20231201 user4  # 返回1（新增）
```

## 3. HyperLogLog算法原理？

### 基本思想
1. **哈希分桶**：将元素哈希后分配到不同桶中
2. **前导零计数**：统计哈希值的前导零个数
3. **调和平均**：使用调和平均数估算基数
4. **偏差修正**：修正小基数和大基数的偏差

### 算法步骤
```
1. 对元素进行哈希：hash(element)
2. 取前b位作为桶号：bucket = hash[0:b]
3. 计算剩余位的前导零：leadingZeros = clz(hash[b:])
4. 更新桶的最大前导零数：M[bucket] = max(M[bucket], leadingZeros)
5. 基数估算：E = α * m² / Σ(2^(-M[j]))
```

### 误差分析
- **标准误差**：1.04/√m（m为桶数）
- **Redis实现**：16384个桶，误差约0.81%
- **置信区间**：68%概率误差在±0.81%内

## 4. HyperLogLog与其他方案对比？

### 内存使用对比
| 方案 | 内存使用 | 精确度 | 适用场景 |
|------|----------|--------|----------|
| Set | O(n) | 100% | 小数据量，需要精确值 |
| Bitmap | O(max_value/8) | 100% | 连续整数ID |
| HyperLogLog | 12KB | 99.19% | 大数据量，可接受误差 |
| BloomFilter | 可配置 | 存在误判 | 存在性检查 |

### 性能对比
```redis
# Set方式（精确但内存大）
SADD uv:20231201 user1 user2 user3
SCARD uv:20231201

# HyperLogLog方式（近似但内存小）
PFADD uv:20231201 user1 user2 user3
PFCOUNT uv:20231201
```

## 5. HyperLogLog的应用场景？

### 网站统计
```redis
# 日UV统计
PFADD uv:20231201 user1 user2 user3
PFCOUNT uv:20231201

# 月UV统计（合并多天数据）
PFMERGE uv:202312 uv:20231201 uv:20231202 ... uv:20231231
PFCOUNT uv:202312

# 页面UV统计
PFADD page:home:uv user1 user2
PFADD page:product:uv user2 user3
```

### 用户行为分析
```redis
# 活跃用户统计
PFADD active:20231201 user1 user2 user3

# 新用户统计
PFADD new_users:20231201 user4 user5

# 留存用户分析
PFMERGE retention:day1 active:20231201 active:20231202
```

### 大数据场景
```redis
# IP访问统计
PFADD ip:20231201 *********** ***********

# 设备指纹统计
PFADD device:20231201 device1 device2

# 商品浏览统计
PFADD product:1001:viewers user1 user2 user3
```

## 6. HyperLogLog的限制？

### 功能限制
- **只能计数**：无法获取具体元素
- **不支持删除**：无法删除已添加的元素
- **存在误差**：基于概率算法，有0.81%误差
- **不支持交集**：无法直接计算两个集合的交集

### 适用条件
- **大数据量**：元素数量很大时优势明显
- **可接受误差**：业务可以接受小幅误差
- **只需计数**：不需要获取具体元素
- **内存敏感**：对内存使用有严格要求

## 7. HyperLogLog优化技巧？

### 数据分片
```redis
# 按时间分片
PFADD uv:2023:12:01 user1 user2
PFADD uv:2023:12:02 user3 user4

# 按业务分片
PFADD mobile:uv user1 user2
PFADD web:uv user3 user4
```

### 定期合并
```redis
# 定期合并历史数据
PFMERGE uv:2023:12 uv:2023:12:01 uv:2023:12:02 ... uv:2023:12:31

# 删除原始数据节省内存
DEL uv:2023:12:01 uv:2023:12:02 ... uv:2023:12:31
```

### 监控误差
```redis
# 对比精确值验证误差
SADD exact_uv user1 user2 user3
PFADD hll_uv user1 user2 user3

SCARD exact_uv      # 精确值
PFCOUNT hll_uv      # 估算值
```

## 8. 实际应用案例？

### 电商平台UV统计
```redis
# 商品页面UV
PFADD product:1001:uv user1 user2 user3
PFCOUNT product:1001:uv

# 类目页面UV
PFADD category:electronics:uv user1 user4 user5
PFCOUNT category:electronics:uv

# 全站UV（合并所有页面）
PFMERGE site:total:uv product:1001:uv category:electronics:uv
PFCOUNT site:total:uv
```

### 广告系统
```redis
# 广告曝光去重
PFADD ad:1001:exposure user1 user2 user3
PFCOUNT ad:1001:exposure

# 广告点击去重
PFADD ad:1001:click user1 user3
PFCOUNT ad:1001:click

# 计算CTR
# CTR = 点击UV / 曝光UV
```

### 社交平台
```redis
# 内容浏览统计
PFADD post:1001:viewers user1 user2 user3
PFCOUNT post:1001:viewers

# 用户关注者统计
PFADD user:1001:followers user2 user3 user4
PFCOUNT user:1001:followers
```

## 9. 面试高频问题

### Q1: HyperLogLog的原理是什么？
**答案**：
1. **哈希分桶**：将元素哈希后分到不同桶
2. **前导零统计**：统计哈希值的前导零个数
3. **调和平均**：使用调和平均数估算基数
4. **偏差修正**：修正算法偏差

### Q2: 为什么HyperLogLog只需要12KB内存？
**答案**：
- Redis使用16384个桶（2^14）
- 每个桶存储6位前导零计数
- 总内存：16384 × 6 bits = 12KB
- 固定内存，不随元素数量增长

### Q3: HyperLogLog的误差来源是什么？
**答案**：
1. **哈希冲突**：不同元素可能产生相同哈希值
2. **概率估算**：基于概率统计，非精确计算
3. **桶数限制**：桶数有限，影响精度
4. **边界效应**：极小或极大基数时误差较大

### Q4: 什么时候选择HyperLogLog而不是Set？
**答案**：
**选择HyperLogLog**：
- 数据量很大（百万级以上）
- 只需要计数，不需要具体元素
- 内存资源有限
- 可以接受小幅误差

**选择Set**：
- 数据量较小
- 需要精确计数
- 需要获取具体元素
- 需要集合运算

### Q5: HyperLogLog如何处理数据倾斜？
**答案**：
1. **多重哈希**：使用多个哈希函数
2. **桶均衡**：确保数据均匀分布到各桶
3. **偏差修正**：算法内置偏差修正机制
4. **监控检查**：定期检查各桶的分布情况

## 10. HyperLogLog最佳实践

### 设计原则
- **合理分片**：按时间或业务维度分片
- **定期合并**：合并历史数据节省内存
- **误差监控**：定期验证统计精度
- **备份策略**：重要统计数据要备份

### 性能优化
- **批量添加**：一次添加多个元素
- **异步处理**：统计操作异步化
- **缓存结果**：缓存计算结果
- **分布式计算**：大数据量时分布式处理

### 运维建议
- **内存监控**：监控HLL内存使用
- **精度验证**：定期对比精确值
- **数据清理**：及时清理过期数据
- **容量规划**：根据业务增长规划容量
