# Redis基础概念面试题

## 1. Redis的数据类型及应用场景？

**String（字符串）**：
- **应用场景**：缓存、计数器、分布式锁、session存储
- **常用命令**：SET、GET、INCR、DECR、MSET、MGET

**Hash（哈希）**：
- **应用场景**：存储对象信息、用户资料、商品详情
- **常用命令**：HSET、HGET、HMGET、HGETALL、HDEL

**List（列表）**：
- **应用场景**：消息队列、最新消息列表、时间线
- **常用命令**：LPUSH、RPOP、LRANGE、LTRIM、BLPOP

**Set（集合）**：
- **应用场景**：标签系统、好友关系、数据去重、抽奖系统
- **常用命令**：SADD、SMEMBERS、SINTER、SUNION、SREM

**ZSet（有序集合）**：
- **应用场景**：排行榜、延时队列、范围查询
- **常用命令**：ZADD、ZRANGE、ZRANK、ZREM、ZCOUNT

## 2. Redis为什么这么快？

1. **内存存储**：数据全部存储在内存中，避免磁盘IO
2. **单线程模型**：避免线程切换开销和锁竞争
3. **IO多路复用**：使用epoll/kqueue高效处理网络IO
4. **高效数据结构**：针对不同场景优化的底层数据结构
5. **简单协议**：RESP协议解析简单，减少序列化开销

## 3. Redis单线程模型的演进？

**Redis 6.0之前**：
- 网络IO和命令执行都在单线程中完成
- 通过IO多路复用技术处理多个客户端连接
- 命令执行严格按顺序进行，保证数据一致性

**Redis 6.0之后**：
- **网络IO多线程化**：读写socket操作可以并行处理
- **命令执行仍单线程**：保证数据操作的原子性和一致性
- **性能提升**：提高网络IO处理能力，减少网络延迟影响

## 4. Redis的底层数据结构？

**SDS（Simple Dynamic String）**：
- 动态字符串，支持O(1)获取长度
- 二进制安全，可存储任意数据

**ziplist（压缩列表）**：
- 紧凑的序列化数据结构
- 节省内存，但插入删除复杂度高

**skiplist（跳跃表）**：
- 有序数据结构，支持范围查询
- 平均O(logN)的查找复杂度

**hashtable（哈希表）**：
- 支持O(1)的查找、插入、删除
- 渐进式rehash避免阻塞

## 5. Redis与Memcached的区别？

**数据类型**：
- Redis：支持5种基本数据类型
- Memcached：只支持简单的key-value

**持久化**：
- Redis：支持RDB和AOF持久化
- Memcached：不支持持久化

**分布式**：
- Redis：原生支持集群
- Memcached：需要客户端实现分布式

**内存管理**：
- Redis：支持虚拟内存和内存淘汰
- Memcached：使用slab分配器

## 6. Redis的应用场景？

**缓存系统**：
- 热点数据缓存
- 数据库查询结果缓存
- 页面缓存

**会话存储**：
- 分布式session共享
- 用户登录状态管理

**消息队列**：
- 简单的消息队列实现
- 发布订阅模式

**计数器**：
- 网站访问统计
- 点赞数、评论数统计

**排行榜**：
- 游戏排行榜
- 热门文章排序

**分布式锁**：
- 防止重复操作
- 资源互斥访问

## 7. Redis的数据过期策略？

**三种策略**：
1. **定时删除**：设置定时器，到期立即删除（CPU消耗大）
2. **惰性删除**：访问时检查是否过期（内存占用大）
3. **定期删除**：定期随机检查并删除过期key（折中方案）

**Redis采用**：惰性删除 + 定期删除的组合策略

## 8. Redis的RESP协议？

**特点**：
- 简单易实现
- 人类可读
- 错误处理简单

**数据类型**：
- 简单字符串：+OK\r\n
- 错误：-Error message\r\n
- 整数：:1000\r\n
- 批量字符串：$6\r\nfoobar\r\n
- 数组：*2\r\n$3\r\nfoo\r\n$3\r\nbar\r\n

## 9. Redis的发布订阅模式？

**基本概念**：
- Publisher：消息发布者
- Subscriber：消息订阅者
- Channel：消息频道

**相关命令**：
- PUBLISH：发布消息
- SUBSCRIBE：订阅频道
- UNSUBSCRIBE：取消订阅
- PSUBSCRIBE：模式订阅

**应用场景**：
- 实时消息推送
- 系统解耦
- 事件通知

**注意事项**：
- 消息不持久化
- 订阅者离线会丢失消息
- 不保证消息送达
