# Redis持久化策略面试题

## 1. Redis有哪些持久化方式？

### RDB（Redis Database Backup）
**原理**：定期生成数据快照保存到磁盘

**优点**：
- 文件紧凑，适合备份
- 恢复速度快
- 对Redis性能影响小

**缺点**：
- 可能丢失最后一次快照后的数据
- fork子进程时会阻塞主进程

**触发方式**：
- 手动：SAVE、BGSAVE命令
- 自动：配置save规则（如save 900 1）
- 其他：主从复制、SHUTDOWN时

### AOF（Append Only File）
**原理**：记录每个写操作命令到文件

**优点**：
- 数据安全性高，最多丢失1秒数据
- 文件可读，便于分析和修复
- 支持秒级、毫秒级、实时同步

**缺点**：
- 文件体积大
- 恢复速度慢
- 对性能影响较大

**同步策略**：
- `always`：每个写命令立即同步（最安全，性能最差）
- `everysec`：每秒同步一次（推荐，平衡安全和性能）
- `no`：由操作系统决定（性能最好，安全性最差）

## 2. AOF重写机制

### 触发条件
- AOF文件大小超过阈值（默认64MB）
- AOF文件增长比例超过100%
- 手动执行BGREWRITEAOF命令

### 重写过程
1. **fork子进程**：创建子进程进行重写，不阻塞主进程
2. **生成新AOF**：子进程根据内存数据生成新AOF文件
3. **双写机制**：主进程同时写入AOF缓冲区和重写缓冲区
4. **合并数据**：重写完成后，将重写缓冲区数据追加到新AOF文件
5. **原子替换**：原子性替换旧AOF文件

### 面试要点
- 为什么需要重写？（减少AOF文件大小，提高恢复速度）
- 重写期间如何保证数据不丢失？（双写机制）

## 3. 混合持久化（Redis 4.0+）

### 工作原理
- **AOF重写时**：先写入RDB格式的全量数据，再追加增量AOF数据
- **文件结构**：RDB数据 + AOF增量命令
- **恢复过程**：先加载RDB部分，再重放AOF部分

### 优势对比
| 方面 | 纯AOF | 混合持久化 |
|------|-------|------------|
| 文件大小 | 大 | 小 |
| 恢复速度 | 慢 | 快 |
| 数据安全性 | 高 | 高 |
| 兼容性 | 好 | 需要4.0+ |

### 配置启用
```
aof-use-rdb-preamble yes
```

## 4. 持久化策略选择

### 场景分析
| 场景 | 推荐策略 | 原因 |
|------|----------|------|
| 缓存场景 | RDB或不持久化 | 数据可重建，性能优先 |
| 数据库场景 | 混合持久化 | 兼顾安全性和性能 |
| 读多写少 | RDB | 性能影响小 |
| 写多读少 | AOF everysec | 平衡性能和安全 |
| 高可用要求 | AOF + RDB | 双重保障 |

### 配置建议
- **生产环境**：混合持久化（aof-use-rdb-preamble yes）
- **缓存场景**：RDB（save 900 1 300 10 60 10000）
- **关键数据**：AOF everysec + RDB备份
- **性能敏感**：RDB + 定期备份到其他机器

## 5. 面试高频问题

### Q1: RDB和AOF可以同时开启吗？
**答案**：可以。Redis启动时优先加载AOF文件，因为AOF数据更完整。

### Q2: BGSAVE和SAVE的区别？
**答案**：
- **SAVE**：阻塞主进程生成RDB文件，会影响服务
- **BGSAVE**：fork子进程生成RDB文件，不阻塞主进程

### Q3: AOF文件损坏怎么办？
**答案**：
1. 使用redis-check-aof --fix修复
2. 手动截断损坏部分
3. 从备份恢复

### Q4: fork子进程会影响性能吗？
**答案**：
- **内存影响**：写时复制机制，理论上不会翻倍
- **CPU影响**：fork瞬间会有短暂阻塞
- **优化**：避免在高峰期进行持久化操作

### Q5: 如何减少RDB文件大小？
**答案**：
- 设置合理的过期时间
- 清理无用的key
- 使用压缩算法
- 选择合适的数据结构

### Q6: AOF重写期间宕机会怎样？
**答案**：
- 重写失败，继续使用原AOF文件
- 不会丢失数据，因为原AOF文件完整
- 重启后会重新触发AOF重写