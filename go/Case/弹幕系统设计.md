### 系统设计题："腾讯视频"实时弹幕系统

**面试官**：你好，我们来聊一个你肯定很熟悉的功能。腾讯视频每天有海量的用户在线观看视频，并发送弹幕。请你设计一个能支撑亿级用户、千万级QPS的实时弹幕系统。

你需要考虑以下几点：
1.  **实时性**：用户发送的弹幕，如何在秒级甚至毫秒级内被房间内其他用户看到？
2.  **高并发**：如何应对热门剧集、顶级赛事直播时，瞬间涌入的弹幕发送和读取请求？
3.  **海量存储与查询**：历史弹幕需要被持久化。当用户拖动进度条时，如何高效地拉取对应时间点的历史弹幕？
4.  **内容安全**：如何设计一个有效的反垃圾、反作弊系统？

---

**候选人**：好的，面试官。这是一个非常经典的实时互动系统设计，挑战与机遇并存。我的设计思路如下：

#### a. 整体架构

这是一个集实时消息推送、高通量数据写入、海量数据查询于一体的复杂系统。我会将其拆分为四大核心模块：弹幕发送、实时分发、离线存储和历史拉取。

*   **数据流向:**
    *   **发送路径 (热)**: 客户端 -> 负载均衡 -> API网关 -> 弹幕发送服务 (Go) -> Kafka消息队列
    *   **分发路径 (热)**: Kafka消息队列 -> 实时推送服务 (Go/WebSocket) -> 客户端
    *   **存储路径 (冷)**: Kafka消息队列 -> 弹幕存储服务 (Go) -> 持久化数据库 (TiDB/Cassandra)
    *   **拉取路径 (冷)**: 客户端 -> 负载均衡 -> API网关 -> 弹幕查询服务 (Go) -> Redis缓存 -> 持久化数据库

*   **模块解析:**
    1.  **弹幕发送接口 (Submission API)**: 一个无状态的Go服务集群。它负责接收客户端的弹幕请求，进行基础校验、内容安全初审（如敏感词过滤）、用户频率限制，然后将合法的弹幕数据（包含`video_id`, `user_id`, `content`, `color`, `position`, `video_timestamp`等）封装成标准格式，异步推送到Kafka中。
    2.  **消息队列 (Message Queue)**: 选用Apache Kafka。它是整个系统的核心缓冲层，用来削峰填谷，解耦实时推送和后端存储。所有弹幕先进入这里，保证了前端发送接口的低延迟响应。Topic可以按`video_id`进行分区，方便下游消费者并行处理。
    3.  **实时推送服务 (Real-time Push Service)**: 这是一个独立的、保持长连接的服务集群，技术栈选用Go+WebSocket。它消费Kafka中的实时弹幕，根据`video_id`找到正在观看该视频的所有客户端连接，并将弹幕毫秒级推送下去。
    4.  **弹幕存储服务 (Storage Service)**: 另一个独立的消费者组，同样消费Kafka中的弹幕。它的任务是将数据持久化到数据库中，供历史查询使用。这个过程是异步的，其性能波动不影响实时弹幕的收发。
    5.  **弹幕查询服务 (Query Service)**: 当用户打开视频或拖动进度条时，客户端会请求这个服务，拉取指定时间段内的历史弹幕。该服务会优先查询Redis缓存，缓存未命中再回源到持久化DB。

#### b. 实时消息推送

这是保证用户"实时"体感的关键。

*   **技术选型**: **WebSocket**。
*   **理由**:
    *   **全双工通信**: 建立持久连接后，服务器可以随时向客户端主动推送数据，延迟极低。
    *   **协议开销小**: 相比HTTP轮询，Header开销小得多。
    *   **事实标准**: 浏览器和移动端支持良好。
*   **海量连接管理**:
    *   需要一个专门的**连接层网关**，每个网关节点维护成千上万的WebSocket连接。
    *   连接信息（`user_id`, `video_id` <=> `connection_id`, `gateway_node_ip`）必须存储在外部的注册中心，如Redis或etcd中。
    *   当实时推送服务从Kafka消费到一条`video_id=V1`的弹幕时，它会去注册中心查询所有观看V1的用户连接在哪台网关节点上，然后通过RPC通知相应的网关节点，由网关将消息推送给客户端。
*   **消息路由**:
    *   为了提高效率，可以对Kafka Topic按`video_id`进行分区。每个推送服务实例只消费一部分分区。
    *   推送服务内部可以进一步通过`video_id`将消息路由到不同的goroutine处理，避免单点阻塞。

#### c. 存储选型

核心挑战是**海量写入**和**时间范围查询 (Range Query)**。

*   **持久化存储**:
    *   **选型**: 放弃传统的MySQL。最优选是**分布式时序数据库 (Time-Series DB) 或宽表模型数据库**，如 **TiDB**, **Cassandra**, 或 **ScyllaDB**。
    *   **理由**: 它们天然为海量写入和大规模分布式扩展设计。TiDB对MySQL协议的兼容性使其迁移和使用成本更低。Cassandra的LSM-Tree架构非常适合写密集的场景。
    *   **表结构设计 (以TiDB/MySQL为例)**:
        ```sql
        CREATE TABLE danmaku (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            video_id BIGINT UNSIGNED NOT NULL,       -- 视频ID
            video_timestamp INT UNSIGNED NOT NULL,   -- 视频内时间戳 (毫秒)
            user_id BIGINT UNSIGNED NOT NULL,        -- 用户ID
            content VARCHAR(255) NOT NULL,           -- 弹幕内容
            -- 其他字段如 color, font_size, created_at 等
            PRIMARY KEY (id),
            INDEX idx_video_time (video_id, video_timestamp) -- 核心索引！
        )
        PARTITION BY HASH(video_id) PARTITIONS 1024; -- 按video_id进行哈希分区
        ```
        `idx_video_time`这个联合索引是高效查询的关键，可以快速定位到某个视频的某个时间点附近的弹幕。使用分区表将不同视频的数据打散到不同的物理存储上，避免单一热点视频压垮整个数据库。

*   **缓存层**:
    *   **选型**: Redis Cluster。
    *   **目的**: 缓存热点视频、高频访问时间段的历史弹幕，降低对后端DB的压力。
    *   **数据结构**: **Sorted Set (ZSET)** 是完美的选择。
        *   **Key**: `danmaku:history:{video_id}:{time_bucket}`。`time_bucket`是将视频时长分段的标识，例如每分钟一个bucket（`floor(video_timestamp / 60000)`）。
        *   **Score**: `video_timestamp`
        *   **Member**: 序列化后的弹幕对象 (JSON字符串)。
    *   **查询逻辑**: 当查询`video_id=V1`, `timestamp=95000ms`（即第95秒）时，计算出`time_bucket=1`。使用`ZRANGEBYSCORE danmaku:history:V1:1 90000 99999` 这样的命令，即可O(log(N)+M)复杂度获取该秒内的所有弹幕。
    *   **缓存策略**: 采用**Cache-Aside**模式。查询服务先查Redis，未命中则查询DB，然后将结果写回Redis并设置TTL（例如，热点视频缓存1小时，普通视频5分钟）。

#### d. 高可用与高并发

*   **无状态服务**: 弹幕发送、查询、存储服务都设计为无状态的，可以任意水平扩缩容。
*   **连接层解耦**: WebSocket连接层是唯一有状态的组件，通过将其状态外部化（存入Redis），也实现了自身节点的无状态化，可以随时增删节点。
*   **Kafka削峰**: Kafka是应对流量洪峰的最重要屏障，它承载了所有写入流量，保护了后端脆弱的数据库服务。即使后端存储服务宕机，也不会影响用户发送弹幕的体验。
*   **限流与熔断**:
    *   **API网关层**: 必须配置精细化的限流策略（基于用户ID、IP），防止恶意请求。
    *   **服务调用间**: Go服务之间、服务对缓存/DB的调用，必须集成熔断器（如gobreaker）。当依赖的服务出现故障，快速失败并返回兜底数据（如空列表或通用提示），防止级联雪崩。
*   **异地多活**: 核心组件如Kafka集群、TiDB/Cassandra集群、Redis集群都应部署在多个可用区（AZ），实现机房级别的容灾。

#### e. 内容安全与反作弊

这是一个持续对抗的系统工程。

*   **前置拦截 (在弹幕发送服务中)**:
    *   **敏感词过滤**: 使用高性能的AC自动机算法，加载动态更新的词库（存储在Redis中），在接收弹幕时进行实时匹配和拦截/替换。
    *   **规则引擎**: 基于用户画像和行为频率进行限制。例如：
        *   单用户/IP的发送频率限制。
        *   新注册用户、低等级用户的发送冷却时间。
        *   短时间内发送大量相似/重复内容的行为。
*   **异步审核 (由独立服务消费特定Topic)**:
    *   对于一些疑似违规但机器无法100%确定的内容（如谐音梗、变体字），将其推送到一个专门的审核Topic。
    *   下游的AI审核服务（NLP模型）和人工审核平台会消费这些数据，进行二次判定。审核结果会反过来更新前置的敏感词库和黑名单。
*   **用户信誉系统**:
    *   建立用户信誉分模型。正常发送弹幕加分，被举报或被系统判定违规则扣分。
    *   对于低信誉分的用户，可以采取更严格的策略，如"先审后发"（弹幕进入审核队列，通过后才会被分发），或者"影子禁言"（用户自己能看到弹幕，但其他用户看不到）。
*   **反刷屏机制**:
    *   在实时推送服务中，可以设置一个小的窗口（如1秒），对窗口内涌入的、`video_id`相同且内容高度相似的弹幕进行聚合或丢弃，只推送其中一条，有效防止"弹幕炸弹"刷屏。

通过以上设计，我们可以构建一个兼具高性能、高可用和强安全性的弹幕系统。这套架构的关键在于**分层**、**解耦**和**异步化**，用不同的技术栈解决不同场景下的核心矛盾。 