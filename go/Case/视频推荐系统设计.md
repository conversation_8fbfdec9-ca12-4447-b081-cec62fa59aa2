### 系统设计题："腾讯视频个性化推荐系统"

好的，这是一个典型的需要综合运用大数据、机器学习和高并发工程实践的复杂系统。我的设计思路会围绕"数据"、"算法"、"系统"三个维度展开，确保系统既能算出好结果，又能稳定、快速地提供服务。

#### a. 整体架构

推荐系统的本质是"信息过滤"，所以整体架构可以分为"离线处理"、"近线（实时）处理"和"在线服务"三大部分，形成一个完整的数据驱动闭环。

![推荐系统架构图](https://user-images.githubusercontent.com/13867185/193183861-6d338a8e-2e38-4680-8263-1498e72782e4.png)
*(图片来源网络，仅作示意)*

1.  **数据层 (Data Layer)**：
    *   **行为数据**：用户在客户端的所有交互行为，如点击、播放（尤其是有效播放时长）、点赞、评论、分享、收藏、搜索等。通过埋点SDK上报，经由高可用的日志网关（如自研L5或Nginx+Lua）统一收集到Kafka消息队列中。
    *   **内容数据 (Item Profile)**：视频的元数据，如标题、标签、分类、演员、导演、清晰度、时长等。这些数据通常存储在媒资库（如MySQL）中，通过Canal等工具监听binlog，增量同步到Kafka，并最终构建成内容画像存储于HBase或Elasticsearch中。
    *   **用户画像 (User Profile)**：用户的静态和动态信息。静态信息如注册时填写的性别、地域；动态信息如基于行为计算出的长期兴趣标签、偏好分类等。存储于HBase这类支持海量读写的KV数据库。

2.  **计算层 (Processing Layer)**：
    *   **离线计算 (Offline)**：使用Spark或Flink在Hadoop集群上进行，周期为小时级或天级。
        *   **ETL**：对原始日志进行清洗、关联、聚合。
        *   **特征工程**：构建用户和视频的特征向量，这是模型效果的基石。
        *   **模型训练**：训练复杂的推荐模型，如协同过滤（ALS）、矩阵分解、深度学习模型（如Wide&Deep、DeepFM）等。
        *   **离线召回库生成**：预先计算好一些召回结果，如Item-CF的相似视频列表，存入KV存储。
    *   **近线计算 (Nearline/Streaming)**：使用Flink消费实时的Kafka行为日志，进行秒级到分钟级的计算。
        *   **实时特征更新**：快速更新用户的短期兴趣画像。例如，用户刚看完《繁花》，短期内对王家卫、上海题材的兴趣权重会立刻提升。
        *   **实时模型/规则**：运行一些轻量级的实时召回策略，或更新在线学习模型。

3.  **服务层 (Serving Layer)**：直接面向前端请求，要求毫秒级响应。
    *   **召回 (Candidate Generation)**：这是推荐的第一步，目标是从千万级的视频库中，快速筛选出几百到上千个符合用户兴趣的候选视频。为了保证多样性，会采用多路召回策略：
        *   协同过滤召回（User-CF, Item-CF）
        *   基于内容画像的召回
        *   基于Embedding的向量召回（核心）
        *   热门榜单召回（保底）
        *   最新内容召回
    *   **排序 (Ranking)**：对召回的几百个视频进行精准打分，预测用户对每个视频的喜好程度（如pCTR-预估点击率, pDuration-预估观看时长）。排序模型通常比召回模型更复杂，使用更多维度的特征。
    *   **重排与过滤 (Re-ranking & Filtering)**：在排序结果的基础上，进行最后的微调。
        *   **过滤**：过滤掉用户已观看过、不感兴趣（明确负反馈）或不符合审核标准的视频。
        *   **多样性保证**：避免首页推荐结果全是同一类型或同一演员的视频，提升用户体验（如使用MMR算法）。
        *   **业务规则**：插入运营指定内容、推广内容等。

#### b. 核心算法与模型

1.  **召回层**：
    *   **Item2Vec/Embedding召回**：这是目前工业界的主流和核心。将用户的观看序列看作句子，视频ID看作单词，使用Word2Vec算法（或其变种）学习出每个视频的Embedding向量。在线上，获取用户最近观看的N个视频的向量，求平均得到用户兴趣向量，然后利用高效的向量搜索引擎（如Faiss、Milvus）找出与该用户向量最相似的Top K个视频向量。这种方式泛化能力强，能发现用户的潜在兴趣。
    *   **双塔模型**：分别构建用户塔（User Tower）和物品塔（Item Tower）两个独立的DNN，输出用户Embedding和物品Embedding。模型优化的目标是让用户与其发生过正向行为的物品的向量在空间中更接近。这是目前各大厂的主流Embedding方案。

2.  **排序层**：
    *   **模型选型**：从传统的LR、GBDT，到目前主流的深度学习模型。
        *   **Wide & Deep**：Google提出的经典模型，结合了LR的记忆能力（Wide部分）和DNN的泛化能力（Deep部分），是工业界非常成熟的方案。
        *   **DeepFM**: 相比W&D，能更好地自动学习特征间的交叉关系。
        *   **DIN (Deep Interest Network)**：阿里提出的模型，针对电商场景设计，核心思想是引入Attention机制。在计算用户对某个候选广告（视频）的兴趣时，会"激活"用户历史行为中与该候选相关的部分，使得兴趣表达更精准。这非常适合视频场景，因为用户的兴趣是多样的。
    *   **目标函数**：单纯预估点击率（CTR）是不够的，因为存在大量"标题党"或"封面党"。更合理的目标是多目标学习（Multi-task Learning），同时预估**点击率、完播率、观看时长、点赞率、分享率**等，最后通过一个加权公式得到最终的排序分：`FinalScore = w1*pCTR + w2*pDuration + w3*pLike + ...`，权重`w`由A/B测试动态调整。

#### c. 存储选型

*   **特征存储**：Redis Cluster。这是在线服务的核心瓶颈之一，要求极低的延迟（1ms级）。存储用户和视频的特征，供排序模型实时查询。Key可以是`u_feat:{user_id}`，Value使用`ProtoBuf`序列化的特征数据。
*   **向量存储/检索引擎**：Faiss或Milvus。Faiss是Facebook开源的库，性能极致，但需要自己封装服务。Milvus是开源的向量数据库，提供了完整的服务化能力，更易于维护。
*   **离线召回结果存储**：HBase或分布式KV。用于存储Item-CF等计算好的相似度矩阵。`RowKey`是`content_id`，列是相似的`content_id`和分数。
*   **用户画像与内容画像**：HBase或Elasticsearch。支持海量数据存储和较快的查询。

#### d. 高可用与高性能

*   **百毫秒级延时挑战**：
    *   **并行召回**：同时向多个召回源发起异步请求，通过`Future/Promise`模式在规定时间内（如80ms）收回结果。
    *   **缓存为王**：服务本地缓存（Caffeine/BigCache）+ 分布式缓存（Redis）是必须的。热门视频的特征和排序结果可以被高频访问，缓存命中率直接影响性能。
    *   **模型推理优化**：使用ONNX Runtime或TensorRT等推理引擎加速深度学习模型的在线计算。对模型进行量化、剪枝等操作。
*   **千万级QPS挑战**：
    *   **服务化与解耦**：将召回、排序、特征服务等拆分为独立的微服务，通过gRPC进行通信，每个服务都可以独立扩缩容。
    *   **弹性伸缩**：整体架构部署在K8s上，配置HPA，根据CPU、内存、QPS等指标自动伸缩。
    *   **降级与熔断**：推荐是非核心链路，必须有完善的降级预案。
        *   任何一个召回通道超时或失败，不影响其他通道返回。
        *   排序模型服务超时，可以降级为使用一个更简单的模型，甚至直接使用召回的原始分数。
        *   整个推荐服务如果失败，不能影响用户看视频的主流程，可以直接返回一个全局热门榜单作为兜底数据。
        *   所有外部依赖调用（Redis, HBase, gRPC）都必须有Hystrix/Go-Breaker等熔断器包裹。

#### e. 系统评估与迭代

推荐系统没有最好，只有更好，持续迭代是关键。

*   **离线评估**：使用AUC、GAUC（Group AUC，比AUC更能反映个性化效果）、Precision@K等指标，在历史数据集上快速验证新模型、新特征的有效性，是上线前的重要参考。
*   **在线A/B测试**：这是评估系统效果的唯一标准。
    *   建立完善的A/B测试平台，支持按用户ID或设备ID进行流量分桶，保证同一用户在实验周期内始终命中同一策略。
    *   核心观察指标：**人均播放时长、CTR、留存率（次日、7日）**。这些是真正反映用户满意度和业务价值的"北极星"指标。
*   **探索与利用 (Exploration & Exploitation)**：为了避免"信息茧房"，需要引入探索机制。
    *   **Exploitation**：给用户推荐他们最可能喜欢的。
    *   **Exploration**：给用户推荐一些他们可能喜欢的新东西。可以通过给新视频或冷门视频加"探索分"，或者使用Bandit算法来动态平衡。
*   **反馈闭环**：用户的线上行为是下一轮模型训练的养料。这个闭环的速度（小时级vs天级）决定了系统捕捉热点和用户兴趣变化的速度。

总的来说，设计一个优秀的推荐系统，需要算法工程师、数据工程师和后端工程师的紧密协作，是一个不断迭代、优化的过程。 