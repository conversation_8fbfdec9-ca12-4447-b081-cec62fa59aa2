### 系统设计题："腾讯视频短视频Feed流系统"

好的，这是一个非常核心的系统，直接关系到用户留存、使用时长和商业变现，挑战与价值并存。我的设计思路会围绕"如何让用户持续不断地刷下去并感到满意"这个核心目标展开，主要分为以下几个部分：

#### a. 整体架构

Feed流系统的本质是一个"召回 -> 粗排 -> 精排 -> 重排"的漏斗模型，并结合了在线服务和离线计算。

![Feed流系统架构图](https://user-images.githubusercontent.com/839091/193233829-1950a263-2735-425c-81b0-64531818b209.png)
*(注：此图是一个经典的推荐系统架构，Feed流系统可在此基础上进行特化)*

1.  **数据源 (Data Sources):**
    *   **用户画像 (User Profile):** 包括静态信息（如年龄、性别、地域）和动态的长期、短期兴趣标签（如"喜欢看科幻片"、"最近爱看萌宠"）。
    *   **内容库 (Content Profile):** 视频的元数据，如分类、标签、关键词、封面信息、创作者信息、BGM等。
    *   **实时行为 (Real-time Behaviors):** 用户在App内的所有操作，特别是针对视频的播放、完播、点赞、评论、分享、不感兴趣等，这些是训练模型最关键的信号。

2.  **离线/近线计算层 (Offline/Near-line Processing):**
    *   **召回 (Recall):** 这是最关键的第一步，目标是从千万级的视频库中，为每个用户初步筛选出几千个可能感兴趣的视频候选项。为了保证多样性和覆盖率，必须采用多路召回策略：
        *   **模型召回:** 基于用户和物品的Embedding向量，使用DSSM、Swing、ERNIE等深度模型计算相似度，找出用户可能喜欢的视频。这是最主要的召回通道。
        *   **协同过滤:** 基于User-CF（与你相似的人还喜欢看什么）、Item-CF（看过这个视频的人还喜欢看什么）进行召回。
        *   **热门召回:** 基于全局、或特定区域/人群的热门内容进行托底召回，解决冷启动问题。
        *   **社交网络召回:** 基于用户关注的创作者、或好友喜欢的视频进行召回。
        *   **运营策略召回:** 用于强行插入运营指定的活动、推广内容。
    *   **召回结果存储:** 各路召回计算出的`user_id -> [video_id_list]`结果，存储在KV存储中，如Redis或分布式KV数据库中。

3.  **在线服务层 (Online Service):**
    *   **Feed流服务 (Feed Service):** 这是核心的在线服务，当用户打开App或下拉刷新时触发。
        *   **获取召回集:** 从Redis中拉取该用户的多路召回结果。
        *   **特征拼接 (Feature Stitching):** 实时获取用户、视频、上下文的最新特征，用于排序。
        *   **排序 (Ranking):** 使用在线排序模型对几千个候选项进行打分，预测用户对每个视频的喜好程度（如预估播放时长、点赞率）。
        *   **重排/混排 (Re-ranking/Mixing):** 在精排结果的基础上，进行最终调整。包括：
            *   **去重:** 过滤掉用户近期已经看过的视频。
            *   **打散:** 避免连续出现同一创作者、同一BGM或同一类型的视频。
            *   **插入内容:** 在合适的位置插入广告、活动等运营内容。
    *   **生成Feed队列:** 将最终排序好的 `video_id` 列表写入一个专属于该用户的Feed队列缓存中（通常是Redis List）。

4.  **客户端 (Client):**
    *   客户端每次从服务端的Feed队列中拉取一页（如10个）视频ID。
    *   根据ID列表，再去内容服务获取视频的详细信息（播放URL、封面、文案等）进行播放。
    *   实现预加载机制，当用户刷到第N个视频时，提前请求下一页数据，保证无限下滑的流畅体验。

#### b. 核心模块设计：召回与排序

1.  **召回层 (Recall):**
    *   **技术选型:** 主力是基于Embedding的向量召回。我们会为每个用户和每个视频训练一个高维向量（Embedding）。
    *   **实现:**
        *   使用用户的历史行为序列（播放、点赞等）通过双塔模型（如DSSM）或图神经网络（GNN）训练出User Embedding和Item Embedding。
        *   将所有视频的Item Embedding构建成向量索引，存入Milvus或Faiss这类专业的向量检索数据库。
        *   当需要为用户召回时，用User Embedding去Milvus中进行ANN（近似最近邻）搜索，高效找出最相似的Top K个视频。

2.  **排序层 (Ranking):**
    *   **目标:** 排序模型是一个多目标预估模型，需要同时预估：pCTR（点击率）、pPlayDuration（播放时长）、pLike（点赞率）、pShare（分享率）等。最终的分数是这些预估值的加权组合：`Score = w1*pCTR + w2*pPlayDuration + ...`。
    *   **模型选型:** 业界常用的是深度学习模型，如**Wide & Deep**或**DeepFM**。
        *   **Wide部分**负责记忆，利用交叉特征，记住"什么样的人在什么场景下会喜欢什么样的视频"这种比较明确的规则。
        *   **Deep部分**负责泛化，通过Embedding将稀疏特征转化为稠密向量，挖掘潜在的、未知的兴趣关联。
    *   **特征工程:** 特征是模型的基石，分为三类：
        *   **用户特征:** 年龄、性别、手机机型、网络环境、长期兴趣、短期实时兴趣。
        *   **视频特征:** 内容分类、关键词、BGM、创作者粉丝数、历史CTR/点赞率。
        *   **上下文特征:** 当前时间（工作日/周末、白天/深夜）、地理位置。

#### c. 存储选型

*   **Feed队列缓存:** **Redis List**。每个用户一个List，`Key: feed_queue:{user_id}`。通过`LPUSH`和`RPOP`或`LRANGE`实现队列管理，性能极高。
*   **"已读"过滤:** **Bloom Filter / Cuckoo Filter**。当用户看过一个视频后，将其ID加入布隆过滤器。在重排阶段，用它来高效判断视频是否已读，避免重复推荐。该过滤器也存储在Redis中。
*   **召回结果:** **Redis Hash**。`Key: recall_result:{user_id}`，`Field: {recall_channel_name}`，`Value: [video_id_list]`。
*   **用户画像/内容库:** **HBase/Cassandra**。这类NoSQL数据库适合存储海量的、结构化的用户和内容数据，支持高并发读写。
*   **向量索引:** **Milvus/Faiss**。专门用于处理海量向量数据的快速检索。

#### d. 高可用与高并发

*   **无状态服务:** Feed流的在线服务必须是无状态的，便于在K8s中快速、水平地扩缩容。
*   **多级缓存:**
    *   **客户端缓存:** 客户端可以缓存用户刷过几页的视频ID和元数据。
    *   **CDN:** 视频内容本身（mp4文件、封面图）必须上CDN。
    *   **服务侧缓存:** 大量使用Redis作为核心流程的缓存。
*   **服务降级与熔断:**
    *   **降级:** 如果复杂的精排模型出现故障或超时，可以降级为只使用粗排模型，甚至直接使用热门召回的结果作为托底，保证用户总有内容可看。
    *   **熔断:** 对所有下游依赖（Redis、HBase、模型服务等）配置熔断器。当某个依赖持续失败时，快速熔断，返回兜底数据或错误，防止请求堆积导致雪崩。
*   **读写分离:** 对于Feed队列，可以采用预生成的模式。由一个离线任务为活跃用户计算好第二天的Feed队列并推送到缓存中。用户读取时，直接消费即可，实现读写分离。

#### e. 核心挑战与解决方案

*   **冷启动问题:**
    *   **新用户:** 通过用户的注册信息（如选择的兴趣）进行初步推荐，然后推送全局或同类人群的热门视频，快速收集其反馈，完成初次画像构建。
    *   **新视频:** 利用内容的元数据（文本、BGM、分类）计算其初始Embedding，将其与热门视频的Embedding进行比对，推荐给可能喜欢这类视频的用户，完成"探索性"分发。
*   **多样性与探索 (EE问题):**
    *   **多样性:** 在重排阶段，使用MMR（最大边界相关）算法或简单的规则（如一个窗口内同作者/同BGM视频不超过N个）进行打散。
    *   **探索 (Exploration):** 在推荐时，不能总推荐用户最喜欢的（Exploitation），也要适度引入用户可能喜欢的新类型内容。可以在排序时，给新类型或用户历史行为中较少的类型一定的加权，或者采用Thompson Sampling、UCB等算法来平衡探索与利用。
*   **实时反馈:** 用户的行为必须被快速捕捉并应用到后续的推荐中。用户的"上滑"（不感兴趣）是极强的负反馈。
    *   **解决方案:** 建立一个近实时的流处理任务（基于Flink/Spark Streaming）。该任务消费实时的用户行为日志，在分钟级的时间窗口内更新用户的短期兴趣模型，这个短期模型会作为在线排序模型的一个重要特征输入。

通过以上设计，我们可以构建一个高并发、高可用、体验流畅且能持续学习演进的短视频Feed流系统。 