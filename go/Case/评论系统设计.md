系统设计题："腾讯视频海量评论系统"设计

好的，这是一个非常核心的互动系统，深入探讨它的设计能够很好地考察候选人对高并发、高可用、数据一致性以及复杂业务场景的综合处理能力。

我们今天来探讨一下：**"腾讯视频海量评论系统"应该如何设计？**

你需要充分考虑腾讯视频的用户体量，一个热门剧集或事件可能在短时间内引来数百万甚至上千万的评论和互动。请从架构、存储、性能、可用性等角度，谈谈你的设计方案。

这是我的回答：

好的，面试官。这是一个非常经典的挑战，它对系统的写入吞吞、读取性能、数据一致性和内容安全都提出了极高的要求。我的设计思路会围绕"**异步化、缓存化、服务化**"这三个核心原则展开。

### a. 整体架构与数据流

我会将整个系统拆分为几个关键的服务和层次，实现关注点分离。

**数据流向:**

1.  **写流程（发布评论/点赞）- 核心是削峰填谷:**
    *   **客户端** 发起请求，携带内容、`video_id`等信息。
    *   **API网关 (Gateway)** 进行统一的认证、限流、黑白名单过滤。
    *   **评论核心服务 (Go)**：这是一个无状态的Go服务。它只做核心的业务逻辑校验（如字数限制、用户状态检查），然后将评论数据（包含`user_id`, `video_id`, `content`, `ip`, `device_id`等丰富的上下文信息）封装成一个消息，**投递到Kafka消息队列**中。
    *   **立刻响应**: 消息进入Kafka后，就立刻向客户端返回"评论成功，正在审核中"的提示，实现快速用户响应。
    *   **下游异步消费**:
        *   **写入消费服务**: 订阅Kafka，批量将评论数据写入主存储（如TiDB），并更新缓存。
        *   **内容安全服务**: 同样订阅Kafka，对评论进行异步的机审和人审。
        *   **数据分析服务 (Flink)**: 订阅点赞、回复等事件，用于实时计算热评。

2.  **读流程（拉取评论）- 核心是多级缓存:**
    *   **客户端** 请求某个视频的评论列表（如按热度、按时间）。
    *   **API网关** 转发请求。
    *   **评论核心服务** 接收请求，开始逐层查询：
        1.  **查询本地缓存 (In-Process Cache)**: 如`FreeCache`，缓存极热视频的评论列表ID，有效期秒级。
        2.  **查询分布式缓存 (Redis Cluster)**: 如果本地缓存未命中，则查询Redis。Redis中会缓存计算好的热评列表、最新评论列表（通常只存`comment_id`），以及每个`comment_id`对应的内容详情。
        3.  **查询数据库 (TiDB)**: 如果Redis也未命中（比如查询一个很老的视频或翻页到很后面），则回源到数据库查询。
        4.  **数据回写**: 从数据库查到数据后，会将其回写到Redis和本地缓存中，供后续请求使用。

### b. 数据存储选型与Schema设计

存储是本系统的基石，需要同时满足高写入、快速查询和海量数据存储的能力。

*   **主存储：TiDB 或 Aurora (MySQL分库分表)**
    *   **选型理由**: 我倾向于选择TiDB。它兼容MySQL协议，更重要的是它天然支持水平扩展，对业务层透明，避免了手动分库分表的复杂维护工作。这对于评论这种可以无限增长的业务至关重要。
    *   **分片策略 (如果用MySQL)**: 会选择`video_id`作为分片键，这样同一个视频下的评论可以落在同一个库或表中，便于分页查询。但要小心热点视频导致单个分片压力过大。
    *   **核心表设计**:
        *   `comment_main` (评论主表):
            *   `comment_id` (bigint, PK): 使用雪花算法生成，保证趋势递增，利于索引。
            *   `video_id` (bigint, Shard Key, Index): 视频ID。
            *   `user_id` (bigint, Index): 用户ID。
            *   `content` (text): 评论内容。
            *   `status` (tinyint): 0-审核中, 1-通过, 2-驳回, 3-用户删除。
            *   `root_id` (bigint, Index): "楼层"的根评论ID，用于实现"盖楼"回复。如果是一级评论，`root_id`为0或自身ID。
            *   `parent_id` (bigint): 回复的上一级评论ID。
            *   `create_time` (datetime)。
        *   `comment_stat` (评论统计表): `comment_id` (PK), `like_count`, `reply_count`等。这张表可以和主表冗余，也可以分离，通过定时任务或Flink实时更新。

*   **缓存：Redis Cluster**
    *   **选型理由**: 性能极高，数据结构丰富，是构建高性能读取服务的首选。
    *   **数据结构**:
        *   **热评列表**: `Sorted Set`。`Key: "hot_comments:{video_id}"`, `Score: 热度分`, `Member: comment_id`。
        *   **最新评论列表**: `Sorted Set`。`Key: "new_comments:{video_id}"`, `Score: timestamp`, `Member: comment_id`。
        *   **评论内容缓存**: `Hash`。`Key: "comment_content:{comment_id}"`, `Fields: "user_info", "content", "like_count", ...`。将一个评论的多个字段存在一个Hash中，减少Key的数量。

*   **检索引擎：Elasticsearch**
    *   **作用**: 为运营后台提供强大的评论搜索和筛选能力（如按用户、按时间段、按内容关键词搜索）。
    *   **同步**: 通过Canal订阅TiDB的binlog，将评论数据准实时同步到Elasticsearch中。

### c. 高并发设计

*   **写并发**:
    *   **核心是异步化**: 如上所述，通过Kafka将同步的写操作转为异步的消费过程，是应对写入洪峰最有效的手段。Kafka集群本身具备极高的吞吐能力和水平扩展性。
*   **读并发**:
    *   **多级缓存**: `CDN -> 本地缓存 -> Redis -> DB` 的多级缓存体系能过滤掉99%以上的数据库请求。
    *   **读写分离**: Redis采用主从架构，读请求可以打到从节点，分担主节点压力。
    *   **热点发现与预热**: 对于可预期的热点事件（如热门大剧开播），可以提前将`video_id`推送到一个热点服务中，评论核心服务可以根据这个列表对缓存进行预热，甚至对特定`video_id`的请求采用更激进的缓存策略。

### d. 核心功能实现细节

*   **"盖楼"与"回复" (Threaded Replies)**
    *   通过`root_id`和`parent_id`字段构建评论的树状结构。
    *   拉取一级评论时，`WHERE root_id = 0`。
    *   当用户点击"查看回复"时，根据该评论的`comment_id`作为`root_id`去拉取楼中楼的评论。为了优化，可以在一级评论中冗余前几条热门回复，减少一次API请求。

*   **"热评"计算 (Hot Comment Calculation)**
    *   这是一个典型的流式计算场景。我会使用**Flink**来处理。
    *   **数据源**: Flink消费一个专门的`comment_interaction_events` Topic，里面包含点赞、取消点赞、回复等事件。
    *   **计算逻辑**:
        1.  `keyBy(comment_id)`: 将同一个评论的事件聚合到同一个Task。
        2.  使用窗口（如Tumbling Window，每分钟计算一次）和状态（`ValueState`存储点赞数）来计算每个评论的最新分数。
        3.  **热度公式**: `Score = W1 * like_count + W2 * reply_count - W3 * time_decay_factor(create_time)`。权重和衰减因子可配置，确保新评论也有机会上榜。
        4.  **输出**: Flink计算出Top N结果后，直接更新到Redis的`hot_comments:{video_id}`这个Sorted Set中。

### e. 内容安全与反作弊

这是保证社区氛围的关键，必须前置考虑。

*   **审核机制**:
    *   **机审为主**: 评论数据进入内容安全服务后，首先通过DFA（确定性有限自动机）进行关键词过滤，然后调用公司内部的AI模型进行文本、图像的语义和情绪分析。给出`pass`, `review`, `reject`结果。
    *   **人工审核**: `review`状态的评论会进入人审后台，由运营团队处理。
    *   **反馈闭环**: 人审的结果需要反馈给AI模型，作为样本进行持续学习和优化。
*   **反垃圾/反作弊**:
    *   **频率限制**: 在API网关层，对`user_id`, `ip`, `device_id`维度进行发评论、点赞的频率限制。
    *   **用户信誉系统**: 对于新注册、低等级的用户，其评论可以自动进入更严格的审核流程。
    *   **行为分析**: 离线分析用户的行为模式，识别"水军"账号（如短时间在大量视频下发布相似评论、批量点赞等），将其加入黑名单。

这个设计方案兼顾了性能、可用性、扩展性和业务复杂性，能够有效支撑腾讯视频这样体量的评论系统。 