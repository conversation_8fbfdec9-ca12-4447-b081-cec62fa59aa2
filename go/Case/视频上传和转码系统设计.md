系统设计题："腾讯视频高可用视频上传和转码系统"
好的，这是一个非常核心的系统，它的稳定性和效率直接影响用户体验和平台运营成本。我的设计思路如下：

### a. 整体架构

这是一个典型的分布式媒体处理工作流系统。我将其划分为以下几个核心模块：

1.  **客户端 (Client)**：负责视频文件的分片、上传和状态上报。
2.  **API网关 (API Gateway)**：所有请求的入口，负责鉴权、路由、限流。
3.  **上传接入服务 (Upload Ingest Service)**：一个Go编写的无状态服务。核心职责是处理上传请求，特别是生成上传凭证（如预签名URL），并将处理任务写入工作流系统。
4.  **对象存储 (Object Storage)**：例如腾讯云的COS或AWS S3。分为两个Bucket：一个用于存储用户上传的原始视频（`source-bucket`），一个用于存储转码后的多规格视频（`transcoded-bucket`）。这是存储的基石。
5.  **工作流与任务调度系统 (Workflow & Task Dispatch System)**：这是整个处理流程的大脑。
    *   **消息队列 (Message Queue)**：使用Kafka或Pulsar。当上传服务确认原始视频分片合并完成后，会向一个`transcoding-jobs`的Topic发送一条消息。
    *   **工作流引擎 (Workflow Engine)**：对于复杂的、需要状态跟踪和长周期运行的任务，我会考虑使用如`Cadence`或`Temporal`这样的专业工作流引擎。如果流程相对固定，也可以用`Argo Workflows`或自研的简单状态机实现。
6.  **媒体处理集群 (Media Processing Cluster)**：
    *   **转码服务 (Transcoding Service)**：一组消费者，订阅`transcoding-jobs`消息，拉取任务。核心是使用`FFmpeg`等工具执行视频的转码、截图、加水印等操作。这个服务会部署在Kubernetes上，并使用`KEDA`（Kubernetes-based Event-Driven Autoscaling）根据Kafka的队列长度自动伸缩Pod数量。
    *   **内容审核服务 (Content Moderation Service)**：转码过程中或完成后，会调用该服务进行AI审核（涉黄、涉政、暴恐）和人工审核。
7.  **元数据服务 (Metadata Service)**：
    *   使用MySQL（分库分表）或TiDB存储视频的元信息，如`video_id`、`user_id`、标题、描述、审核状态、各规格视频的播放地址等。
    *   使用Redis缓存热点视频的元数据，加速读取。

**处理流程图:**
```
            +-------------+       1. 请求上传凭证       +----------------------+
用户客户端  |             | ------------------------> |  API网关 -> 上传服务  |
            +-------------+                           +----------------------+
                   |                                             | 2. 返回预签名URL
                   | 3. 分片上传 (直接传给COS)                       |
                   v                                             |
            +-------------+                           +----------------------+
            |  COS/S3     | <-----------------------+ |  元数据服务 (MySQL)  |
            | (Source)    |       5. 触发工作流          |                      |
            +-------------+ ------------------------> +----------------------+
                   | 4. 上传完成，通知服务                    ^ 8. 更新视频状态/地址
                   |                                        |
+------------------v------------------+       +-------------------------------+
|  工作流系统 (Kafka + Temporal)      | ----> |  媒体处理集群 (K8s)           |
|  (创建转码、审核、发布任务)          |       |  - 转码服务 (FFmpeg)           |
+-----------------------------------+       |  - 审核服务 (AI + 人工)        |
                                            +-------------------------------+
                                                      | 7. 处理结果存回
                                                      v
                                                 +-------------+
                                                 |   COS/S3    |
                                                 | (Transcoded)|
                                                 +-------------+
```

### b. 上传流程优化 (解决大文件上传难题)

直接上传一个几GB的大文件是不可靠的，网络一中断就得重头再来。核心优化策略是**分片上传 (Chunked Upload)** 和 **断点续传 (Resumable Upload)**。

1.  **客户端预处理**：客户端在上传前，先向"上传接入服务"请求一个`upload_id`，并将文件按固定大小（如5MB）切片。
2.  **并发上传**：客户端可以并发上传多个分片，充分利用带宽。每个分片的上传都是独立的HTTP PUT请求。
3.  **上传到存储而非服务器**：为了避免流量经过我们的业务服务器，上传接入服务会为每个分片生成一个有时效性的**预签名URL (Pre-signed URL)**，客户端拿着这个URL直接将数据写入对象存储（COS/S3）。这极大地降低了我们自身服务的带宽压力和成本。
4.  **断点续传与校验**：客户端每上传成功一个分片，就在本地记录下来。如果上传中断，下次恢复时，它可以向服务端查询哪些分片已经成功（服务端可以通过查询对象存储的接口得知），然后只上传剩余的分片。每个分片都应附带MD5哈希值，以便服务端在合并时进行完整性校验。
5.  **完成与合并**：所有分片上传完成后，客户端调用"上传接入服务"的一个`complete`接口，并附上所有分片的ETag列表。服务收到通知后，会向对象存储发起一个`Complete Multipart Upload`的指令，由对象存储在内部完成分片的合并，形成一个完整的原始视频文件。这个合并操作在对象存储内部执行，速度极快。

### c. 异步转码与工作流

视频转码是CPU密集型和耗时操作，必须异步执行。

1.  **任务触发**：当原始视频合并成功后，可以由"上传接入服务"直接向Kafka发送消息，也可以配置COS/S3的事件通知功能，当有新文件写入`source-bucket`时，自动触发一个云函数（SCF/Lambda），由该函数向Kafka发送消息。我更倾向于后者，因为它将上传和处理解耦得更彻底。
2.  **工作流定义**：一条消息代表一个主任务。消息体包含`source_object_key`, `video_id`等。媒体处理集群消费到消息后，会启动一个工作流，典型的Stage包括：
    *   `Pre-processing`: 获取视频元数据（分辨率、码率、时长）。
    *   `Parallel Transcoding`: 根据预设的模板（如 1080p, 720p, 480p），并行地启动多个转码子任务。
    *   `Generate Thumbnails`: 截取关键帧作为封面。
    *   `Content Moderation`: 将视频流或截图送去AI审核。如果AI判定为高风险，则转入人工审核队列，并暂停发布。
    *   `Post-processing & Publish`: 所有任务成功后，将转码后各规格视频的地址、审核状态等信息更新回元数据数据库，并将视频状态置为"已发布"。
3.  **容错与重试**：工作流中的任何一步都可能失败（比如某个转码节点崩溃）。工作流引擎需要支持自动重试（例如，带有指数退避的重试策略）。如果重试多次仍然失败，则将其移入"死信队列"(Dead-letter Queue)，并触发告警，由工程师介入排查。

### d. 存储选型

*   **视频文件**：**对象存储 (COS/S3)** 是不二之选。它提供了近乎无限的扩展性、高持久性（99.999999999%），且成本远低于磁盘。通过设置不同的存储类型（标准、低频、归档），可以对冷热数据做生命周期管理，进一步优化成本。
*   **视频元数据**：
    *   **MySQL (分库分表)**：对于结构化的视频信息、用户信息、审核状态等，MySQL是很好的选择。随着视频量增长，需要基于`video_id`或`user_id`进行水平分库分表。
    *   **NoSQL (可选)**：对于一些非结构化或半结构化的数据，如用户标签、视频的 Embedding 向量（用于推荐），使用MongoDB或HBase可能更合适。
*   **缓存**：**Redis Cluster**。用于缓存视频元数据、用户信息、CDN回源时的热门视频播放地址等。

### e. 高可用与弹性伸缩

1.  **无状态服务**："上传接入服务"、"媒体处理集群中的转码服务"都必须设计成无状态的。这样它们可以被部署在Kubernetes中，根据负载（如CPU使用率、Kafka队列长度）轻松地进行水平自动扩缩容（HPA/KEDA）。
2.  **数据库高可用**：MySQL采用主从复制、双主或MGR集群模式，实现读写分离和故障转移。Redis使用Sentinel或Cluster模式保证高可用。
3.  **对象存储高可用**：这是云厂商的责任，通常他们会提供跨区域复制的功能，以应对单个可用区（AZ）级别的故障。
4.  **隔离与降级**：
    *   **队列隔离**：不同优先级的转码任务（例如VIP用户的视频、短视频）可以使用不同的Kafka Topic，分配不同的消费资源，避免低优任务挤占高优任务的资源。
    *   **服务降级**：在极端情况下（如媒体处理集群大规模故障），可以暂时关闭非核心的转码规格（如4K），只保证主流规格（如720p）的产出。甚至可以降级为"先审后发"，即上传后需要等待审核通过才能播放，保证内容安全。

### f. 内容安全审核

这是视频平台的生命线，必须深度集成在工作流中。

1.  **AI预审核**：在转码的同时，就可以将视频流送给AI进行分析。常见的审核维度包括：图像（涉黄、暴恐、政治敏感人物）、音频（ASR转文字后的文本内容）、OCR（识别视频中的文字）。AI会给出一个分数和建议（通过、建议复审、驳回）。
2.  **人工审核**：对于AI判定为"建议复审"或高风险的视频，会自动进入人工审核后台的队列中。审核员会看到AI标注出的问题片段，进行最终裁定。
3.  **先发后审 vs 先审后发**：
    *   对于高信誉度的用户（如认证的UP主），可以采用"先发后审"策略，即视频上传成功即可被观看，同时后台进行审核。如果发现问题，立即下线。这能最大化用户体验。
    *   对于新用户或低信誉度用户，必须采用"先审后发"策略，视频只有在审核通过后才可见。
4.  **样本库与模型迭代**：所有的人工审核结果都应被用来反馈给AI模型，作为训练样本，持续优化AI审核的准确率。 