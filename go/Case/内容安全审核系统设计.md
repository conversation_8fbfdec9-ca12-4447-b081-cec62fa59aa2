# 系统设计题："腾讯视频内容安全审核系统"设计

**问题背景：**
作为腾讯视频的资深后台专家，请你设计一个全面的内容安全审核系统。该系统需要能够应对每日数以百万计的新增UGC（用户生成内容），包括长/短视频、封面图、评论、弹幕、用户头像和昵称等，进行高效、准确、实时的自动化和人工审核。请详细阐述你的整体架构、核心模块设计、存储选型、以及如何保证系统的高性能、高可用和策略的灵活性。

好的，这是一个极具挑战性的任务。我的设计思路将围绕"分层化、自动化、平台化、数据驱动"的核心思想展开，具体如下：

### a. 整体架构

这是一个典型的集成了大数据处理、AI算法和人工工作流的复杂系统。我设计的架构分为以下几个核心层次：

1.  **统一接入与分发层 (Unified Ingestion & Dispatching):**
    *   所有UGC内容（视频、图片、文本等）的创建/修改请求首先进入此层。
    *   视频、封面等二进制文件通过客户端SDK直传至**对象存储(COS/S3)**，避免占用业务服务器带宽。
    *   元数据及文本内容（如评论、弹幕、视频标题）连同文件在COS的地址，封装成统一格式的消息，发送到**消息队列(Kafka)**。根据内容类型（video, comment, danmaku）分发到不同的Topic。这是系统削峰填谷、异步解耦的第一道关口。

2.  **智能调度与工作流引擎 (Intelligent Scheduling & Workflow Engine):**
    *   这是审核系统的大脑。它消费Kafka中的消息，并为每一个审核对象（content\_id）创建一个审核工作流实例。
    *   我会选择一个成熟的工作流引擎（如Temporal/Cadence），或者基于K8S + Redis FSM自研一个轻量级引擎。
    *   它根据预设的策略（如：新用户的视频需要先审后发，高等级用户的评论可以先发后审），动态构建审核流程（DAG图），决定调用哪些AI模型、是否需要人工审核、以及执行的顺序。

3.  **AI自动审核矩阵 (AI Moderation Matrix):**
    *   一组围绕不同安全维度的AI原子能力微服务集群，部署在GPU K8S集群上。
    *   **视觉层(Vision):** 涉黄、涉暴、涉政、恶心、logo/二维码检测。
    *   **音频层(Audio):** ASR（语音转文本），识别语音中的违规内容。
    *   **文本层(Text):** NLP（自然语言处理），对标题、评论、弹幕以及ASR和OCR的结果进行色情、辱骂、垃圾广告、违禁词的识别。
    *   **多模态融合:** 更高级的模型会融合视觉、文本等多种信息进行综合判断。
    *   所有AI服务都提供统一的同步/异步调用接口，并向调度中心返回带有置信度分数和风险标签的结果。

4.  **人工审核平台 (Human Moderation Platform):**
    *   当AI审核结果的置信度低于某个阈值，或触发了高风险规则，亦或是随机抽样检查时，调度引擎会将任务推送至此平台。
    *   这是一个精细化的后台管理系统，为审核员提供高效的工具，包括：
        *   **任务队列:** 按优先级、技能组（如外语、特定领域）自动分配任务。
        *   **审核工作台:** 集成视频播放器、图片预览、文本高亮，并能精准展示AI预警的时间戳或区域，辅助审核员快速决策。
        *   **质量控制:** 支持"背靠背"双审、仲裁机制，确保审核标准的一致性和准确性。

5.  **策略与决策引擎 (Policy & Decision Engine):**
    *   汇集AI和人工审核的所有结果。
    *   基于一个**可动态配置的规则引擎**（如Drools或自研DSL），对结果进行最终裁决：通过、屏蔽、删除、或仅自己可见。
    *   规则可以非常灵活，例如："`IF (AI.porn_score > 0.95) OR (Human.result == 'porn') THEN Decision.Reject`"。

6.  **处置与反馈中心 (Enforcement & Feedback Center):**
    *   执行决策引擎的裁决结果。通过RPC或消息通知下游业务方（如推荐、搜索、播放）更新内容状态。
    *   将所有审核过程数据（包括AI分数、人工标签、决策结果）沉淀到**数据仓库/数据湖(ClickHouse/Hadoop)**，用于后续的分析、报表和模型训练。

### b. 核心模块设计

1.  **工作流引擎的设计：**
    *   **状态持久化:** 每个审核任务的当前状态（如`pending_ai_video_check`）和上下文必须持久化（如使用MySQL/TiDB），确保服务重启或崩溃后任务能从断点处继续，保证不丢不重。
    *   **动态与可观测性:** 工作流的定义应该是动态的（从配置中心加载），而非硬编码。整个流程需要有详尽的日志和监控，能清晰地看到每个任务的耗时、路径和瓶颈。

2.  **AI审核矩阵的性能优化：**
    *   **级联推理(Cascade Inference):** 为节约昂贵的GPU资源，设计级联策略。例如，先用一个轻量级的CPU模型对视频进行快速抽帧和分类，判断是否为纯风景或静态内容。若是，则直接通过或用简化流程；若不是，再送入重量级的GPU模型进行逐帧详细分析。
    *   **异步化与批量处理:** AI推理服务内部会积累少量请求（如100ms或8个请求），进行批处理（Batching），以充分利用GPU并行计算能力，大幅提升吞吐。

3.  **人工审核平台的效率提升：**
    *   **"鹰眼"系统:** 直接将AI标记出的高风险帧、文本片段、音频片段呈现给审核员，让他们能"一针见血"，而不是从头看到尾。
    *   **键盘快捷键:** 设计全键盘操作，最大化审核效率。
    *   **审核员画像:** 记录每个审核员的效率和准确率，用于任务的精准分配和绩效考核。

### c. 存储选型

*   **元数据与任务状态库 (Metadata & Task State DB):**
    *   **MySQL (分库分表) 或 TiDB:** 存储审核对象的元数据、工作流状态、AI和人工审核的最终结果。这类数据有事务性要求，且需要通过ID快速查询。当数据量巨大时，TiDB提供了更好的水平扩展性。
*   **对象存储 (Object Storage):**
    *   **腾讯云COS/AWS S3:** 存储原始的视频、图片文件，成本低、高可用、高持久性。
*   **消息队列 (Message Queue):**
    *   **Kafka:** 承载全平台内容流入，作为数据总线，实现削峰填谷和系统解耦。高吞吐、可持久化、可水平扩展的特性是其不二之- 选。
*   **实时分析与日志仓库 (Real-time Analytics & Log Warehouse):**
    *   **ClickHouse:** 存储所有审核过程的日志和埋点数据。其出色的OLAP性能可以支撑实时的数据看板（监控大盘）、运营报表和问题排查。
*   **策略规则与配置 (Policy & Configuration):**
    *   **分布式配置中心 (如Apollo) 或 Etcd:** 存储审核策略、AI模型阈值、风险等级等。使得运营和算法同学可以不依赖工程发版，实时调整线上策略。

### d. 高可用与高性能

1.  **全链路异步化:** 从内容接入到最终处置，核心路径全部异步化，避免任何单点阻塞影响整体吞吐。
2.  **服务无状态与水平扩展:** 所有AI服务、调度服务、决策服务都设计为无状态，可以部署在K8S上，配置HPA(Horizontal Pod Autoscaler)根据队列积压或CPU/GPU使用率自动扩缩容。
3.  **降级与熔断:**
    *   **服务降级:** 当某个非核心AI模块（如logo识别）故障时，工作流可以跳过该步骤，保证核心审核流程（如涉黄）不受影响。
    *   **熔断机制:** 对所有外部依赖（数据库、缓存、AI服务）的调用都包裹熔断器（如gobreaker）。当依赖持续失败时，快速失败或返回兜底结果，防止雪崩。
4.  **时效性保障 (SLA):**
    *   通过**优先级队列**保证高风险、高热度内容的优先审核。
    *   对视频等耗时长的任务，采用**分片并行处理**的策略。将视频切成小片段（Chunks），并行送入AI服务进行分析，最后由调度中心聚合结果，极大缩短单个视频的审核时间。

### e. 策略与运营的灵活性

1.  **风险分级与差异化策略:**
    *   **用户分级:** 官方认证账号(PGC)、高信用度用户(UGC-High)、普通用户、新用户、低信用度用户，应用完全不同的审核流程和发布策略（例如，高信用用户先发后审，低信用用户先审后发）。
    *   **内容分级:** 根据内容来源、历史表现等，对内容进行风险预判，投入不同的审核资源。
2.  **A/B测试框架:** 任何新策略、新模型的上线都必须经过A/B测试，用数据来验证其效果，避免误伤或漏放。
3.  **反馈闭环与模型自优化:**
    *   这是系统长期发展的核心。人工审核结果（特别是AI判断错误被人工纠正的样本）是极其珍贵的**Hard Case**。
    *   建立一条通畅的**样本回流**数据管道，将这些样本持续不断地送回给算法团队，用于模型的增量训练和迭代，形成一个数据驱动的、不断进化的智能审核系统。 