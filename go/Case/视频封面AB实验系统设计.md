# 系统设计题："腾讯视频封面A/B实验系统"

好的，这是一个非常核心的增长和优化类系统设计题，直接关系到平台的核心指标（如CTR、VTR），体现了数据驱动决策的能力。我的设计思路如下：

## a. 整体架构

A/B实验系统的本质是一个闭环数据系统，包含"分流 -> 实验 -> 收集 -> 分析"四个核心环节。

1.  **实验管理与配置层 (Management & Configuration)**:
    *   提供一个Web平台，供运营和产品经理自助式地创建和管理实验。
    *   **配置内容**: 实验名称、目标视频(或视频集合)、候选封面(A、B、C...)、实验流量(例如10%的用户)、分层(Layer)、目标指标(如点击率CTR、播放转化率VTR)等。
    *   配置信息存储在**MySQL**中，并通过**配置中心**（如Apollo, etcd）实时下发给后端的流量分配服务。

2.  **流量分配与决策服务 (Traffic Allocation & Decision)**:
    *   这是整个系统的入口，要求**极高并发**和**极低延迟**。
    *   客户端（App, Web）在请求视频列表时，会调用此服务（或通过BFF调用）。
    *   服务根据`user_id`和**分层哈希算法**，判断用户是否命中该实验，如果命中，则根据实验配置决定返回哪个封面的URL。
    *   为了性能，实验配置会全量缓存在该服务的**本地内存**中。

3.  **数据采集与上报层 (Data Collection)**:
    *   这是数据闭环的起点，关键是**埋点**的准确性。
    *   **曝光日志**: 当一个带有实验封面的视频在用户屏幕上**实际可见**时，客户端上报一条曝光日志。日志包含`request_id`, `user_id`, `content_id`, `experiment_id`, `group_id` (A/B组), `timestamp`等。
    *   **行为日志**: 用户点击封面、完整播放等行为，作为转化事件上报。日志同样需要携带上述实验相关ID。
    *   所有日志通过高可用的**日志网关**统一接收，发送到**Kafka**消息队列中，按事件类型分发到不同的Topic。

4.  **数据处理与分析层 (Processing & Analysis)**:
    *   这是A/B实验系统的大脑。
    *   **计算引擎**: 优选**Apache Flink**或**Spark Streaming**进行流式处理，实现准实时的效果分析。同时，使用**Spark SQL**或**ClickHouse**进行T+1的离线分析和深度挖掘。
    *   **核心流程**:
        1.  消费曝光日志和行为日志两个Topic。
        2.  使用`request_id`或`(user_id, content_id)`作为Key，进行双流Join，将曝光和其后续的转化行为关联起来。
        3.  按`experiment_id`和`group_id`进行聚合，计算每个实验组的曝光数(PV)和转化数(Click/Play)。
        4.  进行**统计学检验**（如卡方检验、T检验），计算p-value和置信区间，判断实验结果是否**统计显著**。

5.  **结果存储与展示层 (Storage & Presentation)**:
    *   **结果存储**: 计算出的核心指标（PV, Click, CTR, p-value等）按分钟或小时粒度存储在**ClickHouse**或**Druid**这类OLAP数据库中，便于快速多维分析。
    *   **结果展示**: 实验管理平台从OLAP数据库中拉取数据，以图表和表格的形式向用户展示实时的实验报告，清晰地标明哪个版本胜出。

## b. 核心模块1: 流量分配服务

这是用户体验的直接保障，必须做到快速和稳定。

*   **技术选型**: **Go语言**。天然适合高并发、IO密集型的网络服务，协程模型能轻松应对海量请求，且性能极高。
*   **分流算法**:
    *   **分层与分桶**: 为了避免多个实验相互干扰，引入"层(Layer)"的概念。流量先通过`hash(user_id) % 100`落入某个"域"，例如[0-89]是默认流量，[90-99]是实验域。进入实验域的流量，再通过分层隔离。
    *   **层内互斥**: 在同一层内，实验是互斥的。例如L1层做推荐算法实验，L2层做封面实验。用户通过`hash(user_id + layer_id) % 10000`被分配到层内的某个桶。
    *   **桶与实验组**: 实验配置会声明它需要哪些桶。例如，一个5% vs 5%的实验，需要1000个桶。实验组A占用[0-499]，实验组B占用[500-999]。
    *   **一致性保证**: 整个哈希和分桶逻辑必须保证一个用户在实验周期内被稳定地分到同一个组。
*   **配置管理**:
    *   服务启动时从配置中心全量拉取所有在线实验的配置，存储在本地内存（例如一个`map[layer_id] -> Experiment`）。
    *   通过监听配置中心的变化，实时增量更新本地缓存。这种"推拉结合"的模式保证了配置的低延迟访问。
*   **容灾与兜底**:
    *   服务自身必须是无状态的，可水平扩展，部署在K8s上并配置HPA。
    *   如果流量分配服务异常或超时，BFF或客户端必须有**兜底逻辑**，直接请求默认封面，保证核心的视频播放功能不受影响。
    *   监控和告警是生命线，必须对服务的QPS、延迟、错误率以及实验配置的加载情况做精细化监控。

## c. 核心模块2: 数据处理与统计分析

这里的核心是保证数据的准确性和分析的科学性。

*   **数据流**: Flink Job消费Kafka中的`exposure-log`和`click-log`。
*   **数据清洗**: 过滤掉不合规的数据，例如`experiment_id`或`group_id`为空的，或者来自已知爬虫IP的流量。
*   **状态化处理**:
    *   使用`KeyedStream`，以`request_id`为key。
    *   当曝光日志到达时，将其存入`ValueState`，并注册一个定时器（例如10分钟后触发）。
    *   如果在定时器触发前，相应的点击日志到达，则认为发生了一次有效转化，输出`(exposure, click)`对。
    *   如果定时器触发后点击日志仍未到，则认为未转化，输出`(exposure, no-click)`。这解决了数据乱序和延迟到达的问题。
*   **统计学核心**:
    *   **为什么需要统计显著性?** 仅仅比较CTR的绝对值（例如A组5.1% vs B组5.0%）是没有意义的，这个差异可能仅仅是由"随机波动"造成的。我们需要科学地判断这个差异是否足够大，以至于我们可以自信地认为它代表了真实的优劣。
    *   **原假设(H0)**: 两个版本的封面CTR没有差异。
    *   **备择假设(H1)**: 两个版本的封面CTR存在差异。
    *   **检验方法**: 对于CTR这种比率型数据，最常用的是**Z检验或卡方检验**。
    *   **P-value**: 计算出的p-value代表"如果原假设为真，我们观察到当前数据或更极端数据的概率"。通常，当`p-value < 0.05`时，我们拒绝原假设，认为实验结果是**统计显著**的。
    *   **置信区间**: 除了p-value，我们还会计算CTR提升的置信区间，例如"[0.5%, 1.5%]"，这比单个的p-value能给业务方提供更多信息。

## d. 存储选型

*   **实验元数据(Metadata)**: **MySQL**。负责存储实验的配置信息、创建人、状态等。关系型数据库的事务性能保证数据的一致性。
*   **原始上报日志(Raw Logs)**: **Kafka** -> **HDFS/S3**。Kafka作为实时流管道，之后数据会落地到廉价的对象存储中，用于离线归档和后续的复杂分析（如用户LTV分析）。
*   **实时聚合结果(Real-time Aggregates)**: **ClickHouse**。其列式存储和向量化执行引擎，非常适合A/B实验这种**大宽表**、**多维度聚合查询**的场景。前端看板查询`SELECT group_id, sum(is_click) / count(*) as ctr FROM experiment_metrics WHERE experiment_id = 'xxx' GROUP BY group_id`会非常快。
*   **配置与缓存**: **Apollo/etcd** 作为配置中心, **Redis**可以作为可选的二级缓存，但对于流量分配服务，本地缓存通常是性能最好的选择。

## e. 挑战与应对

*   **AA实验**: 在正式实验前，会进行"AA实验"（给两个组分配完全一样的封面）。理论上，AA实验不应有显著差异。这能帮助我们验证分流系统的均匀性和数据上报的准确性。如果AA实验显著，说明系统本身有问题，需要立即排查。
*   **辛普森悖论 (Simpson's Paradox)**: 当我们在一个大的群体上观察到某种趋势（如A优于B），但在该群体的每个子群（如新老用户、安卓/iOS用户）中，趋势却可能完全相反。
    *   **应对**: 必须进行**多维度下钻分析**。在分析总体结果的同时，也要看不同维度下的细分结果，确保结论的普适性。ClickHouse非常擅长这种多维度切片分析。
*   **学习效应/新奇效应 (Learning/Novelty Effect)**: 用户可能会因为一个封面样式新颖而产生更多的点击，但这种效应会随着时间推移而衰减。
    *   **应对**: 实验需要运行足够长的时间（例如1-2周），观察指标是否稳定。不能在实验第一天看到效果暴涨就草率下结论。需要分析实验组和对照组指标的时间序列，看趋势是否趋于平行。

*   **并发实验管理**:
    *   **正交实验**: 使用上文提到的分层机制，确保不同目的的实验（如UI实验 vs 推荐实验）不会相互影响。
    *   **互斥实验**: 在同一层内，实验之间是互斥的，一个用户只能命中一个实验，避免了效应的叠加污染。

## 附录：为何大厂要自建统一实验平台？

    *   **互斥实验**: 在同一层内，实验之间是互斥的，一个用户只能命中一个实验，避免了效应的叠加污染。 