# 如何设计一个高可用的"跨端断点续播"系统？

作为面试官，我会很期待听到候选人对这个问题的深入思考。这是一个非常典型的系统设计题，它考察了对高并发读写、数据一致性、系统容错等多个核心能力的理解。

我的设计思路如下：

### a. 核心挑战与设计考量

"断点续播"是视频类产品的核心用户体验功能。设计这个系统，我们首先要识别出其主要的技术挑战：

1.  **高频写入**: 假设有千万级用户同时在线观看视频，如果每分钟上报一次进度，系统的写入峰值将达到数十万QPS。这对系统的吞吐量和处理能力是巨大的考验。
2.  **低延迟读取**: 用户打开App或点击进入播放页时，期望能"无感"地加载到上次的播放位置。这对读取接口的P99延迟要求非常高，通常需要在50毫秒以内。
3.  **数据一致性**: 用户在手机上看到一半，换到平板或电视上时，必须能从正确的位置继续播放。如何保证多个设备间的状态快速、准确同步，是这个系统的核心难题，尤其要处理好并发写入的冲突问题。
4.  **存储成本**: 需要为上亿的活跃用户存储观看历史，每个用户可能看过成百上千个视频。总数据量可达TB甚至PB级别，必须设计合理的存储方案来平衡成本和性能。
5.  **弱网容错**: 用户的网络环境不稳定，客户端的上报可能会延迟、失败或乱序到达。系统设计必须能应对这种不可靠的网络情况。

### b. 整体架构

这是一个典型的读写分离、异步处理的架构。

![系统架构图](https://user-images.githubusercontent.com/13185339/218329532-6131c77f-1d22-4a0f-90a6-97693006d649.png)

1.  **客户端 (Client)**: 播放器负责在播放过程中，按固定频率（如每30秒）或关键事件（暂停、seek、退出）上报播放进度。为应对弱网，客户端应有本地缓存和失败重传机制。
2.  **接入网关 (API Gateway)**: 高性能的API网关，负责接收客户端上报，完成用户认证、请求校验等基础工作。
3.  **消息队列 (Message Queue)**: 网关将合法的上报请求直接投递到Kafka集群。这是削峰填谷、实现系统解耦的关键。使用`user_id`作为消息的partition key，可以保证同一用户的进度更新事件在同一个分区内是有序的。
4.  **实时处理服务 (Processing Service)**: 一个由Go编写的无状态微服务集群，负责消费Kafka中的消息。它承载了核心的业务逻辑，如数据清洗、冲突解决，并将最终结果写入后端存储。
5.  **数据存储层 (Storage Layer)**: 采用冷热数据分离策略。
    *   **热数据存储**: 使用Redis集群，存储用户最近观看的N条记录（例如1000条）。这是为了满足高并发、低延迟的在线读取需求。
    *   **冷数据存储**: 使用ClickHouse或HBase，全量存储用户所有的观看历史。这些数据用于历史记录查询、数据分析和算法推荐。
6.  **查询服务 (Query Service)**: 另一组由Go编写的微服务，专门负责处理客户端的读取请求。它会优先从Redis查询热数据，如果查询不到（或需要查询更久远的历史），则会穿透到ClickHouse中查询。

### c. 数据模型与存储选型

**1. 热数据存储 (Redis)**

*   **数据结构**: `Hash` 是最佳选择。
    *   `Key`: `history:user_id:{user_id}`
    *   `Field`: `video_id`
    *   `Value`: 一个JSON字符串，包含播放详情。`{"progress": 1234, "ts": 1676200000, "dev": "iphone14", "dur": 7200}` (播放秒数, 最后上报时间戳, 设备, 总时长)
*   **为什么用Hash**:
    *   `HSET` 和 `HGET` 命令可以在O(1)时间内更新或获取单个视频的进度，完美匹配业务需求。
    *   相比为每个`user_id:video_id`创建一个独立的String Key，Hash结构能极大地减少Key的数量，在用户观看记录非常多的情况下，内存效率更高，管理更方便。
    *   可以使用`HGETALL`一次性获取用户的所有近期观看记录，用于"历史记录"列表页的展现。

**2. 冷数据存储 (ClickHouse)**

*   **表结构**:
    ```sql
    CREATE TABLE watch_history (
        user_id UInt64,
        video_id UInt64,
        progress UInt32,
        duration UInt32,
        last_report_ts DateTime,
        device String
    ) ENGINE = MergeTree()
    PARTITION BY toYYYYMM(last_report_ts)
    ORDER BY (user_id, video_id, last_report_ts);
    ```
*   **为什么用ClickHouse**:
    *   极佳的写入性能和压缩比，存储成本低。
    *   基于`user_id`的查询性能非常高，适合历史记录分页查询等场景。

### d. 关键流程设计

**1. 进度上报流程 (写)**

1.  客户端按30秒间隔或在暂停/退出时，将`{user_id, video_id, progress, timestamp, ...}`数据包发送到接入网关。
2.  网关校验后，将消息以`user_id`为key，发送到Kafka的`play_progress_topic`。
3.  处理服务消费消息，首先从Redis用`HGET`获取该`user_id`下该`video_id`的旧记录。
4.  **核心冲突解决**: 比较新上报记录的时间戳`new.timestamp`和Redis中存储的时间戳`old.timestamp`。**只有当`new.timestamp > old.timestamp`时**，才执行更新。这可以有效防止因网络延迟导致的乱序消息覆盖掉最新的进度。
5.  使用`HSET`命令更新Redis中的热数据。
6.  同时，将该条记录异步地发送到另一个Kafka topic，由数据同步服务（如Flink/DataX）批量写入ClickHouse完成持久化。

**2. 进度查询流程 (读)**

1.  用户进入播放页，客户端调用查询服务接口`GET /history/latest?video_id=xxx`。
2.  查询服务直接请求Redis：`HGET history:user_id:{user_id} {video_id}`。
3.  如果Redis命中，则直接返回结果。
4.  如果Redis未命中（例如，因为是冷数据或缓存被淘汰），服务会向ClickHouse发起二次查询。
5.  查询到冷数据后，会将其写回Redis（缓存预热），再返回给客户端，确保下一次访问能够命中缓存。

### e. 高可用与高并发设计

*   **全链路无状态化**: 接入网关、处理服务、查询服务都是无状态的，可以部署在K8s上，利用HPA根据CPU/内存负载进行秒级自动扩缩容。
*   **缓存为王**:
    *   **客户端缓存**: 客户端应在本地（如UserDefaults/SharedPreferences）持久化最后一次的播放进度。这样即使用户离线再打开App，也能立即看到进度条，提升体验。
    *   **服务本地缓存**: 在查询服务内部，可以使用FreeCache或BigCache等高性能本地缓存库，缓存热点用户的查询结果（缓存几秒钟），进一步减少对Redis的请求压力。
*   **Redis高可用**: 采用Redis Cluster架构，实现数据的水平分片和自带的高可用。每个Master节点至少配置一个Slave节点，并开启读写分离，读请求可以打到Slave节点以分摊压力。
*   **降级与熔断**:
    *   **写通路降级**: 如果Kafka集群故障，网关可以临时降级，丢弃上报请求。这对用户是透明的，最坏情况是进度没有及时更新。
    *   **读通路降级**: 如果Redis集群故障，查询服务应立即熔断，快速失败，直接返回空或默认值（如0）。此时客户端可以使用它的本地缓存作为兜底，保证播放体验不受大的影响。

### f. 弱网环境优化

弱网是移动端场景必须面对的问题。

*   **客户端侧**:
    1.  **请求合并**: 在网络良好时，可以将短时间内的多次进度更新（比如用户反复seek）在本地合并，只上报最后一次的结果，减少无效请求。
    2.  **本地队列**: 当上报请求失败时，不应立即丢弃，而是存入本地的一个持久化队列（如使用SQLite）。
    3.  **重试机制**: 后台任务会以指数退避策略（Exponential Backoff）尝试重新发送队列中的失败请求。
*   **服务端侧**:
    *   服务端的时间戳冲突解决机制 (`new.timestamp > old.timestamp`) 能够天然地处理这些延迟到达的、乱序的历史数据，保证最终数据状态的正确性。

通过以上设计，我们可以构建一个能够支撑亿级用户、高并发、高可用的跨端断点续播系统。这个设计在架构的健壮性、扩展性和成本效益之间取得了良好的平衡。 