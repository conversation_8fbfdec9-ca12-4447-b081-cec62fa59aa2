## HTTPS握手过程详解

### TLS握手流程

#### TLS 1.2握手流程（2-RTT）

1. **Client Hello** → 发送支持的加密套件和随机数
2. **Server Hello** → 选择加密套件，发送证书
3. **Key Exchange** → 交换密钥材料
4. **Finished** → 完成握手，开始加密通信

### 关键组件

#### 握手消息类型
| 消息 | 作用 | 内容 |
|------|------|------|
| **Client Hello** | 发起握手 | TLS版本、加密套件、随机数 |
| **Server Hello** | 响应握手 | 选择的套件、证书、随机数 |
| **Certificate** | 身份认证 | 服务器证书链 |
| **Key Exchange** | 密钥协商 | 密钥交换参数 |

#### 密钥生成
- **预主密钥** + **客户端随机数** + **服务端随机数** → **主密钥**
- **主密钥** → 派生出加密密钥、MAC密钥、初始化向量

### 证书验证过程

#### 证书链验证
1. **证书格式验证**：检查证书格式是否正确
2. **有效期验证**：检查证书是否在有效期内
3. **域名验证**：检查证书域名是否匹配
4. **签名验证**：验证证书签名的有效性
5. **吊销检查**：检查证书是否被吊销（CRL/OCSP）

#### 信任链验证
- 验证证书链：服务器证书 ← 中间CA证书 ← 根CA证书
- 确保根证书在受信任的证书存储中

### TLS 1.3改进

#### 主要优势
- **1-RTT握手**：减少往返次数
- **0-RTT恢复**：会话恢复更快
- **更强安全性**：移除不安全算法
- **强制前向安全**：必须使用ECDHE

### 性能优化

#### 会话复用
- **Session ID**：服务端保存会话状态
- **Session Ticket**：客户端保存加密的会话状态
- **0-RTT**：TLS 1.3支持的快速恢复

#### 其他优化
- **OCSP Stapling**：减少证书验证延迟
- **HTTP/2**：多路复用减少连接数
- **证书链优化**：减少证书大小

### 面试要点

**Q: HTTPS握手过程中的关键步骤？**
A: 
1. 协商加密套件和TLS版本
2. 服务端发送证书，客户端验证
3. 密钥交换，生成会话密钥
4. 切换到加密通信

**Q: 如何优化HTTPS握手性能？**
A:
1. 会话复用（Session ID/Ticket）
2. OCSP Stapling减少证书验证延迟
3. 使用TLS 1.3减少握手往返
4. HTTP/2多路复用减少连接数

**Q: TLS 1.2和TLS 1.3的主要区别？**
A:
1. 握手往返次数：2-RTT vs 1-RTT
2. 安全性：移除不安全算法，强制前向安全
3. 性能：支持0-RTT会话恢复

**Q: 证书验证包括哪些步骤？**
A: 格式验证、有效期验证、域名验证、签名验证、吊销检查、信任链验证
