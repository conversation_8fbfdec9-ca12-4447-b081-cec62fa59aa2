## HTTP/2与HTTP/3特性对比

### HTTP版本演进

| 特性 | HTTP/1.1 | HTTP/2 | HTTP/3 |
|------|----------|--------|--------|
| **传输协议** | TCP | TCP | UDP (QUIC) |
| **多路复用** | 无 | 二进制帧多路复用 | 流级别多路复用 |
| **头部压缩** | 无 | HPACK | QPACK |
| **服务器推送** | 无 | 支持 | 支持 |
| **连接建立** | 多次握手 | 1次TCP握手 | 0-RTT/1-RTT |
| **队头阻塞** | 严重 | 应用层解决 | 完全解决 |

### HTTP/2核心特性

#### 1. 二进制分帧
- HTTP/1.1文本协议 → HTTP/2二进制协议
- 更高效的解析和传输

#### 2. 多路复用
- 单连接并发处理多个请求
- 解决HTTP/1.1的连接限制问题

#### 3. 头部压缩(HPACK)
- 减少重复头部传输
- 维护头部索引表，提高压缩率

#### 4. 服务器推送
- 主动推送相关资源
- 减少客户端请求次数

#### 5. 流优先级
- 资源加载优先级控制
- 重要资源优先传输

### HTTP/3核心特性

#### 1. QUIC协议基础
- 基于UDP的可靠传输协议
- 集成加密、多路复用、可靠性

#### 2. 连接建立优化
- 首次连接：1-RTT
- 后续连接：0-RTT
- 显著减少连接延迟

#### 3. 流级别多路复用
- 彻底解决队头阻塞问题
- 单个流的阻塞不影响其他流

#### 4. 连接迁移
- 网络切换时保持连接
- 支持WiFi和移动网络无缝切换

#### 5. 改进的拥塞控制
- BBR算法：基于带宽和RTT
- 更快的丢包检测和恢复
- 更准确的RTT测量

### 性能对比

#### 延迟优化
```
连接建立时间:
HTTP/1.1: DNS + TCP握手 + TLS握手 = 3-RTT
HTTP/2:   DNS + TCP握手 + TLS握手 = 3-RTT  
HTTP/3:   DNS + QUIC握手 = 1-RTT (首次)
          DNS + 0-RTT = 0-RTT (后续)
```

#### 吞吐量提升
```
并发请求处理:
HTTP/1.1: 6个连接 × 1个请求 = 6个并发
HTTP/2:   1个连接 × 无限流 = 高并发
HTTP/3:   1个连接 × 无限流 + 无队头阻塞 = 更高并发
```

### 实际应用场景

#### HTTP/2适用场景
- **Web应用**：大量小资源的网站
- **API服务**：高频率的API调用
- **移动应用**：减少连接数，节省电量

#### HTTP/3适用场景
- **视频流媒体**：对延迟敏感的实时应用
- **移动网络**：网络不稳定的环境
- **IoT设备**：需要连接迁移的场景
- **游戏应用**：低延迟要求的实时游戏

### 部署与兼容性

#### 协议协商
- 客户端和服务端自动协商最高支持版本
- 支持优雅降级：HTTP/3 → HTTP/2 → HTTP/1.1

#### Go语言支持
- **HTTP/2**：Go标准库自动支持（需要HTTPS）
- **HTTP/3**：需要第三方库如quic-go

### 面试要点

**Q: HTTP/2相比HTTP/1.1有什么优势？**
A:
1. **多路复用**：单连接并发处理多个请求
2. **头部压缩**：HPACK减少重复头部传输
3. **服务器推送**：主动推送相关资源
4. **二进制协议**：更高效的解析

**Q: HTTP/3解决了什么问题？**
A:
1. **队头阻塞**：QUIC流级别多路复用彻底解决
2. **连接建立延迟**：0-RTT/1-RTT快速建立连接
3. **连接迁移**：网络切换时保持连接
4. **拥塞控制**：更精确的拥塞控制算法

**Q: 什么时候选择HTTP/3？**
A:
1. 对延迟敏感的应用（视频、游戏）
2. 移动网络环境（网络不稳定）
3. 需要连接迁移的场景
4. 高并发、高吞吐量要求

**Q: HTTP/2的服务器推送有什么注意事项？**
A:
1. 避免推送客户端已有的资源
2. 推送的资源应该是客户端即将需要的
3. 注意推送的资源大小和数量
4. 客户端可以拒绝推送的资源
