## RPC实现原理

### RPC基本概念

RPC（Remote Procedure Call）是一种通过网络调用远程服务的技术，让远程调用像本地函数调用一样简单。

### RPC调用流程

```
客户端                                    服务端
  |                                        |
  |--1. 调用本地Stub函数                    |
  |--2. 序列化参数                          |
  |--3. 网络传输请求----------->           |
  |                            4. 接收请求--|
  |                            5. 反序列化--|
  |                            6. 调用本地函数--|
  |                            7. 序列化结果--|
  |<-----------8. 网络传输响应              |
  |--9. 反序列化结果                        |
  |--10. 返回给调用方                       |
```

### 核心组件

#### 1. 客户端Stub
- **接口代理**：提供与远程服务相同的接口
- **参数序列化**：将参数转换为网络传输格式
- **网络通信**：发送请求并接收响应
- **结果反序列化**：将响应转换为本地对象

#### 2. 服务端Stub
- **请求接收**：监听并接收客户端请求
- **参数反序列化**：将网络数据转换为本地对象
- **服务调用**：调用实际的业务逻辑
- **结果序列化**：将返回值转换为网络传输格式

#### 3. 序列化协议
| 协议 | 特点 | 适用场景 |
|------|------|----------|
| **JSON** | 可读性好，跨语言 | Web API，调试友好 |
| **Protobuf** | 高效，强类型 | 高性能，内部服务 |
| **MessagePack** | 紧凑，快速 | 性能敏感场景 |
| **Thrift** | 跨语言，功能丰富 | 大型分布式系统 |

#### 4. 传输协议
| 协议 | 特点 | 使用场景 |
|------|------|----------|
| **HTTP** | 简单，防火墙友好 | 公网API，Web服务 |
| **TCP** | 高效，可靠 | 内网高性能服务 |
| **UDP** | 快速，低延迟 | 实时性要求高 |
| **HTTP/2** | 多路复用，头部压缩 | 现代Web服务 |

### 服务发现与注册

#### 服务注册
```
服务启动 → 注册到注册中心 → 定期心跳保活
```

#### 服务发现
```
客户端 → 查询注册中心 → 获取服务列表 → 选择实例调用
```

#### 常用注册中心
- **Consul**：功能丰富，支持健康检查
- **Etcd**：高可用，强一致性
- **Zookeeper**：成熟稳定，Java生态
- **Eureka**：Spring Cloud生态

### 负载均衡策略

| 策略 | 特点 | 适用场景 |
|------|------|----------|
| **轮询** | 简单，均匀分布 | 服务器性能相近 |
| **随机** | 实现简单 | 大量请求场景 |
| **加权轮询** | 考虑服务器性能 | 服务器性能不同 |
| **最少连接** | 动态负载均衡 | 长连接场景 |
| **一致性哈希** | 缓存友好 | 有状态服务 |

### 容错机制

#### 1. 超时控制
```go
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()
result, err := client.Call(ctx, "method", args)
```

#### 2. 重试机制
- **固定间隔重试**：简单但可能加重负载
- **指数退避重试**：逐渐增加重试间隔
- **限制重试次数**：避免无限重试

#### 3. 熔断器
```
正常 → 失败率超阈值 → 熔断开启 → 定期尝试 → 恢复正常
```

#### 4. 降级策略
- **返回默认值**：提供基本功能
- **调用备用服务**：使用备选方案
- **缓存结果**：返回历史数据

### Go语言RPC实现

#### 标准库RPC
```go
// 服务端注册和启动
rpc.Register(&Calculator{})
rpc.HandleHTTP()
http.ListenAndServe(":8080", nil)

// 客户端调用
client, _ := rpc.DialHTTP("tcp", "localhost:8080")
client.Call("Calculator.Add", &Args{1, 2}, &result)
```

#### gRPC
- 基于HTTP/2和Protobuf
- 支持多种语言
- 提供强类型接口定义

### 面试要点

**Q: RPC和HTTP API的区别？**
A:
- **RPC**：面向方法，强类型，高性能
- **HTTP API**：面向资源，松耦合，易调试

**Q: RPC如何保证可靠性？**
A:
1. **超时控制**：防止无限等待
2. **重试机制**：处理临时故障
3. **熔断器**：防止雪崩效应
4. **负载均衡**：分散请求压力

**Q: 如何选择序列化协议？**
A:
- **性能要求高**：选择Protobuf、MessagePack
- **跨语言兼容**：选择JSON、XML
- **调试友好**：选择JSON、XML

**Q: 服务发现的作用？**
A:
1. **动态感知**：自动发现新服务实例
2. **故障隔离**：剔除不健康实例
3. **负载均衡**：在多个实例间分配请求
4. **配置管理**：统一管理服务配置