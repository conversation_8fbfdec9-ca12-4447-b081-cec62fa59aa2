## TCP可靠传输保证机制

### 核心机制对比

| 机制 | 作用 | 实现方式 |
|------|------|----------|
| **确认应答** | 保证数据到达 | ACK确认，超时重传 |
| **序列号** | 保证数据顺序 | 序列号标识，重组排序 |
| **流量控制** | 防止接收方过载 | 滑动窗口机制 |
| **拥塞控制** | 防止网络拥塞 | 慢启动、拥塞避免 |
| **校验和** | 保证数据完整性 | 错误检测和丢弃 |

### 1. 确认应答机制

**工作原理**：
- 接收方收到数据后发送ACK确认
- 发送方设置超时时间(RTO)
- 超时未收到ACK则重传

### 2. 序列号机制

**作用**：
- 标识数据包顺序
- 帮助接收方重组数据
- 确认号表示期望接收的下一个字节

### 3. 流量控制

**滑动窗口**：
- 接收方通告窗口大小
- 发送方根据窗口大小控制发送速率
- 防止发送方发送过快

### 4. 拥塞控制

**主要算法**：
- **慢启动**：连接初期逐渐增加发送窗口
- **拥塞避免**：检测到拥塞时减小窗口
- **快速重传**：收到3个重复ACK立即重传
- **快速恢复**：快速重传后快速恢复窗口大小

### 5. 数据完整性

**校验和机制**：
- 发送方计算校验和
- 接收方验证校验和
- 不匹配则丢弃数据包

### 面试要点

**Q: TCP如何保证可靠传输？**
A: 通过确认应答、序列号、流量控制、拥塞控制、校验和五大机制

**Q: 超时重传的时间如何确定？**
A: 基于RTT动态计算RTO，通常RTO = RTT + 4×RTT偏差

**Q: 流量控制和拥塞控制的区别？**
A:
- **流量控制**：防止接收方过载，基于接收窗口
- **拥塞控制**：防止网络拥塞，基于拥塞窗口