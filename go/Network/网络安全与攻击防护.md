## 网络安全与攻击防护

### 常见网络攻击类型

#### 1. DDoS攻击
**定义**：分布式拒绝服务攻击，通过大量请求使服务器过载

**攻击类型**：
- **SYN Flood**：TCP半连接攻击，耗尽服务器连接资源
- **UDP Flood**：发送大量UDP包，消耗带宽和处理能力
- **HTTP Flood**：应用层请求洪水，消耗应用资源
- **DNS放大**：利用DNS响应放大攻击流量

**防护策略**：
- **流量清洗**：CDN和DDoS防护服务过滤恶意流量
- **限流策略**：IP限流、接口限流、连接数限制
- **负载均衡**：分散攻击压力到多台服务器
- **黑洞路由**：丢弃攻击流量

#### 2. SYN Flood攻击详解
**攻击原理**：
1. 攻击者伪造大量源IP发送SYN包
2. 服务器为每个SYN分配资源进入SYN_RCVD状态
3. 攻击者不回复ACK，导致半连接堆积
4. 服务器资源耗尽，无法处理新连接

**防护措施**：
- **SYN Cookies**：不预分配资源，用cookie验证连接
- **系统调优**：调整队列大小、超时时间
- **防火墙规则**：限制SYN包频率和连接数

#### 3. Web应用攻击

##### SQL注入
**攻击原理**：通过输入恶意SQL代码，操控数据库执行非预期查询

**防护措施**：
- **参数化查询**：使用占位符而非字符串拼接
- **输入验证**：白名单验证，过滤特殊字符
- **最小权限原则**：数据库用户权限最小化
- **WAF防护**：Web应用防火墙过滤恶意请求

##### XSS攻击
**类型对比**：
- **存储型XSS**：恶意脚本存储在服务器，危害程度高
- **反射型XSS**：恶意脚本通过URL参数传入，危害程度中
- **DOM型XSS**：通过JavaScript操作DOM执行脚本

**防护措施**：
- **输出编码**：HTML转义用户输入内容
- **CSP策略**：内容安全策略限制脚本执行
- **HttpOnly Cookie**：防止脚本访问Cookie

##### CSRF攻击
**攻击原理**：跨站请求伪造，利用用户已登录状态执行非预期操作

**防护措施**：
- **CSRF Token**：验证请求来源的随机令牌
- **SameSite Cookie**：限制Cookie跨站发送
- **验证Referer头**：检查请求来源页面

#### 4. 中间人攻击（MITM）
**攻击原理**：攻击者拦截并可能修改通信双方的数据

**防护措施**：
- **HTTPS加密**：TLS加密所有数据传输
- **证书验证**：验证服务器身份真实性
- **证书固定**：固定证书指纹防止伪造
- **HSTS**：强制使用HTTPS连接

### HTTPS安全机制

#### 核心安全保障
- **数据加密**：防止数据被窃听
- **身份认证**：通过证书验证服务器身份
- **完整性校验**：确保数据未被篡改

#### 加密技术组合
- **非对称加密**：RSA/ECDHE用于密钥交换
- **对称加密**：AES用于数据传输加密
- **哈希算法**：SHA-256确保数据完整性
- **数字证书**：CA签发，提供身份验证

### 网络防护体系

#### 1. 多层防护架构
**网络层防护**：
- **防火墙**：包过滤、状态检测、应用层防护
- **IDS/IPS**：入侵检测与防护系统
- **DDoS防护**：流量清洗、黑洞路由

**应用层防护**：
- **WAF**：Web应用防火墙，过滤恶意HTTP请求
- **API网关**：统一入口，限流、认证、监控
- **负载均衡**：分散流量，提高可用性

#### 2. 安全配置最佳实践
**网络隔离**：
- 网络分段，限制横向移动
- DMZ区域部署，隔离内外网
- VLAN划分，逻辑隔离不同业务

**访问控制**：
- 最小权限原则
- 白名单机制
- 多因素认证

### 安全监控与响应

#### 1. 安全监控
**监控指标**：
- 异常流量模式
- 失败登录尝试
- 系统资源使用率
- 网络连接状态

**监控工具**：
- **SIEM**：安全信息与事件管理
- **SOC**：安全运营中心
- **威胁情报**：实时威胁信息

#### 2. 应急响应
**响应流程**：
1. **检测**：发现安全事件
2. **分析**：评估影响范围
3. **遏制**：阻止攻击扩散
4. **恢复**：系统功能恢复
5. **总结**：事后分析改进

### 安全开发实践

#### 1. 安全编码
- **输入验证**：严格验证所有输入
- **输出编码**：防止XSS攻击
- **参数化查询**：防止SQL注入
- **错误处理**：避免信息泄露

#### 2. 安全配置
- **最小权限**：只授予必要权限
- **定期更新**：及时安装安全补丁
- **配置基线**：标准化安全配置
- **日志审计**：完整记录操作日志

### 面试要点

**Q: 常见的网络攻击类型有哪些？**
A:
1. **DDoS攻击**：SYN Flood、UDP Flood、HTTP Flood
2. **Web攻击**：SQL注入、XSS、CSRF
3. **中间人攻击**：拦截和篡改通信数据
4. **恶意软件**：病毒、木马、勒索软件

**Q: 如何防护DDoS攻击？**
A:
1. **流量清洗**：CDN和DDoS防护服务
2. **限流策略**：IP限流、接口限流
3. **负载均衡**：分散攻击压力
4. **黑洞路由**：丢弃攻击流量

**Q: XSS和CSRF的区别及防护？**
A:
- **XSS**：注入恶意脚本，防护通过输出编码、CSP策略
- **CSRF**：伪造用户请求，防护通过CSRF Token、SameSite Cookie

**Q: SQL注入的原理和防护？**
A:
- **原理**：拼接恶意SQL代码操控数据库
- **防护**：参数化查询、输入验证、最小权限、WAF

**Q: HTTPS如何保证安全？**
A: 通过三个方面：数据加密（防窃听）、身份认证（防冒充）、完整性校验（防篡改）

**Q: 如何设计安全的网络架构？**
A:
1. **分层防护**：网络层、应用层、数据层多重防护
2. **网络隔离**：DMZ、VLAN、网络分段
3. **访问控制**：最小权限、白名单、多因素认证
4. **监控响应**：实时监控、威胁检测、应急响应
