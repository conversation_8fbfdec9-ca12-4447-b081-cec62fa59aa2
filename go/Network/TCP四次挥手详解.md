## TCP四次挥手详解

### 四次挥手概述

**定义**：TCP连接终止过程，通过四次消息交换安全关闭双向连接。

**为什么需要四次挥手**：
- TCP是全双工通信，需要分别关闭两个方向的数据传输
- 被动关闭方可能还有数据要发送
- 确保所有数据都已传输完毕

### 四次挥手过程

#### 第一次挥手 (FIN)
**主动关闭方 → 被动关闭方**
- 标志位：FIN = 1
- 序列号：seq = u
- 状态变化：ESTABLISHED → FIN_WAIT_1
- 含义：请求关闭连接，不再发送数据

#### 第二次挥手 (ACK)
**被动关闭方 → 主动关闭方**
- 标志位：ACK = 1
- 确认号：ack = u + 1
- 状态变化：ESTABLISHED → CLOSE_WAIT
- 含义：确认收到关闭请求，但可能还有数据要发送

#### 第三次挥手 (FIN)
**被动关闭方 → 主动关闭方**
- 标志位：FIN = 1, ACK = 1
- 序列号：seq = w，确认号：ack = u + 1
- 状态变化：CLOSE_WAIT → LAST_ACK
- 含义：数据发送完毕，请求关闭连接

#### 第四次挥手 (ACK)
**主动关闭方 → 被动关闭方**
- 标志位：ACK = 1
- 确认号：ack = w + 1
- 状态变化：FIN_WAIT_2 → TIME_WAIT
- 含义：确认关闭，进入TIME_WAIT状态

### 连接状态转换

#### 主动关闭方状态转换
```
ESTABLISHED → FIN_WAIT_1 → FIN_WAIT_2 → TIME_WAIT → CLOSED
```

#### 被动关闭方状态转换
```
ESTABLISHED → CLOSE_WAIT → LAST_ACK → CLOSED
```

### 关键状态说明

#### FIN_WAIT_1
- 发送FIN后等待ACK确认
- 如果收到ACK进入FIN_WAIT_2
- 如果收到FIN+ACK直接进入TIME_WAIT

#### FIN_WAIT_2
- 已收到对方ACK确认
- 等待对方发送FIN包
- 半关闭状态，只能接收数据

#### CLOSE_WAIT
- 收到对方FIN包，发送ACK确认
- 应用程序应该关闭连接
- 如果不关闭会导致连接泄漏

#### TIME_WAIT
- 持续时间：2MSL（Maximum Segment Lifetime）
- 作用：确保最后的ACK能够到达对方
- 防止旧连接的数据包干扰新连接

### 异常情况处理

#### 1. FIN包丢失
- 发送方重传FIN包
- 使用指数退避算法
- 达到重传上限后强制关闭

#### 2. ACK包丢失
- 接收方重传FIN包
- 发送方重传ACK包
- 最终正常关闭连接

#### 3. 同时关闭
- 双方同时发送FIN包
- 状态转换：ESTABLISHED → FIN_WAIT_1 → CLOSING → TIME_WAIT → CLOSED

#### 4. 半关闭状态
- 一方关闭发送，另一方继续发送数据
- 应用场景：文件传输完成但需要接收确认

### TIME_WAIT状态详解

#### 存在原因
1. **确保ACK到达**：如果最后的ACK丢失，对方会重传FIN
2. **防止数据干扰**：等待网络中旧数据包消失
3. **端口复用安全**：避免新连接收到旧数据

#### 常见问题
- **端口耗尽**：大量TIME_WAIT占用端口
- **内存消耗**：每个连接占用内存资源
- **性能影响**：影响新连接建立

#### 优化方案
- **SO_REUSEADDR**：允许端口复用
- **调整参数**：减少TIME_WAIT时间
- **连接池**：复用连接减少关闭操作

### 实际应用考虑

#### 应用程序关闭连接
- **正确关闭**：使用defer conn.Close()确保连接关闭
- **优雅关闭**：先关闭写端，读取剩余数据后关闭读端

#### 服务器端优化
- **Keep-Alive**：复用连接
- **连接池**：管理连接生命周期
- **超时设置**：避免连接泄漏

### 面试要点

**Q: TCP四次挥手的具体过程？**
A:
1. **客户端发送FIN**：请求关闭连接
2. **服务器发送ACK**：确认收到，但可能还有数据
3. **服务器发送FIN**：数据发送完毕，请求关闭
4. **客户端发送ACK**：确认关闭，进入TIME_WAIT

**Q: 为什么是四次挥手而不是三次？**
A: TCP是全双工通信，需要分别关闭两个方向的连接。被动关闭方收到FIN后，可能还有数据要发送，所以ACK和FIN分开发送。

**Q: TIME_WAIT状态的作用？**
A:
- 确保最后的ACK能够到达对方
- 防止旧连接的数据包干扰新连接
- 等待网络中延迟的数据包消失

**Q: 如何解决TIME_WAIT过多的问题？**
A:
- 使用连接池复用连接
- 调整系统参数减少TIME_WAIT时间
- 使用SO_REUSEADDR选项
- 优化应用程序连接管理

**Q: CLOSE_WAIT状态过多说明什么问题？**
A: 应用程序没有正确关闭连接，通常是代码bug导致的连接泄漏，需要检查应用程序的连接管理逻辑。
