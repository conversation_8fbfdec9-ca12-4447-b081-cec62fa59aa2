## TCP三次握手详解

### 三次握手概述

**定义**：TCP连接建立过程，通过三次消息交换确保双方都能正常收发数据。

**目的**：
- 确认双方收发能力正常
- 同步序列号(Sequence Number)
- 协商连接参数(MSS、窗口大小等)
- 建立可靠的双向通信通道

### 三次握手详细过程

#### 第一次握手 (SYN)
**客户端 → 服务器**
- **标志位**：SYN = 1
- **序列号**：seq = x (随机初始值)
- **状态变化**：CLOSED → SYN_SENT
- **含义**：客户端请求建立连接

#### 第二次握手 (SYN + ACK)
**服务器 → 客户端**
- **标志位**：SYN = 1, ACK = 1
- **序列号**：seq = y (服务器随机初始值)
- **确认号**：ack = x + 1
- **状态变化**：LISTEN → SYN_RCVD
- **含义**：服务器确认客户端请求，同时请求建立反向连接

#### 第三次握手 (ACK)
**客户端 → 服务器**
- **标志位**：ACK = 1
- **序列号**：seq = x + 1
- **确认号**：ack = y + 1
- **状态变化**：SYN_SENT → ESTABLISHED
- **含义**：客户端确认服务器响应，连接建立完成

### 为什么需要三次握手？

#### 两次握手的问题
- **无法确认客户端接收能力**：服务器不知道客户端是否收到SYN+ACK
- **旧连接请求问题**：网络延迟可能导致旧的SYN包被误认为新连接
- **资源浪费**：服务器可能为无效连接分配资源

#### 三次握手的优势
- **双向确认**：确保双方都能正常收发数据
- **序列号同步**：建立可靠的数据传输基础
- **防止旧连接**：通过序列号验证连接有效性
- **参数协商**：协商MSS、窗口大小等参数

### 连接状态转换

#### 客户端状态转换
```
CLOSED → SYN_SENT → ESTABLISHED
```

#### 服务器状态转换
```
CLOSED → LISTEN → SYN_RCVD → ESTABLISHED
```

#### 完整状态图
- **CLOSED**：初始状态，无连接
- **LISTEN**：服务器监听状态，等待连接请求
- **SYN_SENT**：客户端发送SYN后的等待状态
- **SYN_RCVD**：服务器收到SYN后的等待状态
- **ESTABLISHED**：连接建立完成，可以传输数据

### 参数协商
- **MSS**：最大段大小
- **窗口大小**：流量控制窗口
- **窗口扩大选项**：支持大窗口

### 异常情况处理
- **SYN超时重传**：第一次握手包丢失时重传
- **SYN+ACK超时重传**：第二次握手包丢失时重传
- **ACK丢失**：第三次握手包丢失，服务器重传SYN+ACK
- **同时打开**：双方同时发起连接，四次握手建立

### 安全考虑
- **SYN Flood攻击**：大量伪造SYN包耗尽服务器资源
- **防护措施**：SYN Cookies、连接限制、防火墙过滤

### 性能优化
- **TCP Fast Open**：在SYN包中携带数据，减少RTT
- **连接复用**：HTTP Keep-Alive、HTTP/2多路复用
- **连接池**：预建立连接减少握手开销

### 面试要点

**Q: TCP三次握手的具体过程是什么？**
A:
1. **客户端发送SYN**：seq=x，请求建立连接
2. **服务器回复SYN+ACK**：seq=y, ack=x+1，确认并请求反向连接
3. **客户端发送ACK**：seq=x+1, ack=y+1，确认连接建立

**Q: 为什么是三次握手而不是两次或四次？**
A:
- **两次不够**：无法确认客户端接收能力，可能建立无效连接
- **三次刚好**：确保双方收发能力正常，同步序列号
- **四次多余**：三次已足够建立可靠连接

**Q: 三次握手过程中如果第三次ACK丢失会怎样？**
A:
- **客户端**：认为连接已建立，可以发送数据
- **服务器**：重传SYN+ACK，收到客户端数据后建立连接
- **最终结果**：连接正常建立，有轻微延迟

**Q: 如何防护SYN Flood攻击？**
A:
- **SYN Cookies**：不预分配资源，用cookie验证
- **连接限制**：限制单IP的SYN请求频率
- **防火墙过滤**：过滤异常SYN包
- **负载均衡**：分散攻击压力

**Q: TCP握手过程中协商了哪些参数？**
A:
- **MSS**：最大段大小
- **窗口大小**：流量控制窗口
- **窗口扩大选项**：支持大窗口
- **时间戳选项**：RTT测量和PAWS
- **SACK选项**：选择性确认
