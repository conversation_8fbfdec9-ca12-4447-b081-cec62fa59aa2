## TCP报文头部结构

### TCP头部格式

TCP头部固定长度为20字节，可通过选项字段扩展到最大60字节。

**基本结构**：源端口(16位) + 目的端口(16位) + 序列号(32位) + 确认号(32位) + 头长度(4位) + 标志位(6位) + 窗口大小(16位) + 校验和(16位) + 紧急指针(16位) + 选项(可变)

### 字段详解

| 字段 | 长度 | 作用 | 说明 |
|------|------|------|------|
| **源端口** | 16位 | 发送方端口号 | 0-65535 |
| **目的端口** | 16位 | 接收方端口号 | 0-65535 |
| **序列号** | 32位 | 数据排序 | 标识发送的字节流位置 |
| **确认号** | 32位 | 确认接收 | 期望接收的下一个序列号 |
| **头长度** | 4位 | 头部长度 | 以4字节为单位(5-15) |
| **保留** | 3位 | 预留字段 | 必须为0 |
| **标志位** | 6位 | 控制信息 | URG/ACK/PSH/RST/SYN/FIN |
| **窗口大小** | 16位 | 流量控制 | 接收窗口大小 |
| **校验和** | 16位 | 错误检测 | 头部和数据的校验 |
| **紧急指针** | 16位 | 紧急数据 | URG=1时有效 |

### 标志位详解

#### 6个控制标志位
| 标志 | 全称 | 作用 | 使用场景 |
|------|------|------|----------|
| **URG** | Urgent | 紧急指针有效 | 紧急数据传输 |
| **ACK** | Acknowledgment | 确认号有效 | 除SYN外都设置 |
| **PSH** | Push | 立即推送数据 | 交互式应用 |
| **RST** | Reset | 重置连接 | 连接异常 |
| **SYN** | Synchronize | 同步序列号 | 建立连接 |
| **FIN** | Finish | 结束连接 | 关闭连接 |

#### 标志位组合
- **SYN=1, ACK=0**：连接请求
- **SYN=1, ACK=1**：连接确认
- **FIN=1, ACK=1**：关闭请求
- **RST=1**：强制关闭
- **ACK=1**：数据确认

### 序列号和确认号

#### 工作机制
- **序列号**：标识发送数据的字节流位置
- **确认号**：期望接收的下一个序列号
- **三次握手**：客户端SYN(seq=x) → 服务端SYN+ACK(seq=y,ack=x+1) → 客户端ACK(seq=x+1,ack=y+1)

### 窗口大小与流量控制

- **作用**：防止发送方发送过快，反映接收方缓冲区大小
- **窗口缩放**：实际窗口 = 窗口字段 × 2^缩放因子
- **最大窗口**：65535 × 2^14 = 1GB

### 常用TCP选项

| 选项 | 作用 |
|------|------|
| **MSS** | 最大段大小 |
| **窗口缩放** | 扩大窗口大小 |
| **SACK** | 选择性确认 |
| **时间戳** | RTT测量 |

### 面试要点

**Q: TCP头部的固定长度是多少？**
A: 20字节，通过选项字段可扩展到最大60字节

**Q: TCP的6个标志位分别是什么？**
A: URG(紧急)、ACK(确认)、PSH(推送)、RST(重置)、SYN(同步)、FIN(结束)

**Q: 序列号和确认号的作用？**
A:
- **序列号**：标识发送数据的字节流位置，保证数据有序
- **确认号**：表示期望接收的下一个序列号，实现可靠传输

**Q: 窗口大小字段的作用？**
A: 用于流量控制，表示接收方当前可接收的数据量，防止发送方发送过快

**Q: 为什么需要校验和？**
A: 检测TCP头部和数据在传输过程中是否出现错误，保证数据完整性