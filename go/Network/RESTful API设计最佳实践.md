## RESTful API设计最佳实践

### REST架构原则

#### 1. 资源导向设计
**好的设计**：
- `GET /api/v1/users` - 获取用户列表
- `GET /api/v1/users/123` - 获取特定用户
- `POST /api/v1/users` - 创建用户
- `PUT /api/v1/users/123` - 更新用户
- `DELETE /api/v1/users/123` - 删除用户

**嵌套资源**：
- `GET /api/v1/users/123/orders` - 获取用户的订单

**避免的设计**：
- `GET /api/v1/getUsers` - 动词形式
- `POST /api/v1/createUser` - 动词形式

#### 2. 资源结构设计
**JSON结构规范**：
- 使用snake_case命名字段
- 包含必要的元数据（id, created_at, updated_at）
- 合理的嵌套层级
- 可选字段使用omitempty标签

#### 3. HTTP方法使用

| 方法 | 用途 | 状态码 | 幂等性 |
|------|------|--------|--------|
| GET | 获取资源 | 200 OK | ✓ |
| POST | 创建资源 | 201 Created | ✗ |
| PUT | 更新资源 | 200 OK | ✓ |
| DELETE | 删除资源 | 204 No Content | ✓ |
| PATCH | 部分更新 | 200 OK | ✗ |

#### 4. 状态码使用

**成功状态码**：
- `200 OK` - 成功获取/更新资源
- `201 Created` - 成功创建资源
- `204 No Content` - 成功删除资源

**客户端错误**：
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 权限不足
- `404 Not Found` - 资源不存在
- `409 Conflict` - 资源冲突

**服务器错误**：
- `500 Internal Server Error` - 服务器内部错误

### API版本控制

#### 版本控制策略
1. **URL版本控制**：`/api/v1/users`
2. **请求头版本控制**：`API-Version: v1`
3. **参数版本控制**：`?version=v1`
4. **媒体类型版本控制**：`Accept: application/vnd.api.v1+json`

#### 版本管理原则
- **向后兼容**：新版本不破坏旧版本
- **渐进式废弃**：给出足够的迁移时间
- **文档清晰**：明确版本差异和迁移指南

### 请求验证和错误处理

#### 输入验证原则
- **必填字段检查**：确保必要字段不为空
- **格式验证**：邮箱、手机号等格式校验
- **长度限制**：字符串长度、数值范围检查
- **业务规则验证**：符合业务逻辑的数据校验

#### 错误响应格式
```json
{
  "error": "Validation Failed",
  "message": "The request contains invalid data",
  "code": 400,
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

#### 错误处理策略
- **统一错误格式**：保持错误响应结构一致
- **详细错误信息**：提供足够的调试信息
- **错误分类**：区分客户端错误和服务器错误
- **日志记录**：记录错误详情用于排查

### 面试要点

**Q: RESTful API的设计原则？**
A:
1. **资源导向**：URL表示资源，不是动作
2. **HTTP方法**：使用标准HTTP方法表示操作
3. **无状态**：每个请求包含所有必要信息
4. **统一接口**：一致的API设计风格

**Q: 如何设计API版本控制？**
A: URL版本控制(/api/v1)、请求头版本控制(API-Version)、媒体类型版本控制

**Q: API幂等性如何保证？**
A: GET/PUT/DELETE天然幂等，POST通过幂等键保证

**Q: 如何处理API分页？**
A: 基于偏移量(page/limit)或基于游标(cursor)，响应包含分页元数据

**Q: RESTful vs RPC的区别？**
A: 详见"RestFul 与 RPC 的区别.md"

### 最佳实践总结

1. **资源命名**：使用名词复数形式，小写字母
2. **HTTP方法**：正确使用GET、POST、PUT、DELETE
3. **状态码**：返回合适的HTTP状态码
4. **错误处理**：统一的错误响应格式
5. **版本控制**：向后兼容的版本策略
6. **安全考虑**：认证、授权、输入验证
7. **性能优化**：缓存、分页、压缩
