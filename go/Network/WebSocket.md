## WebSocket协议详解

### 核心概念
WebSocket是基于TCP的全双工通信协议，支持客户端和服务器间的实时双向数据传输。

### WebSocket vs HTTP对比

| 特性 | HTTP | WebSocket |
|------|------|-----------|
| **通信模式** | 请求-响应 | 全双工 |
| **连接方式** | 短连接 | 长连接 |
| **协议开销** | 每次请求都有头部 | 握手后开销小 |
| **实时性** | 需要轮询 | 真正实时 |
| **服务器推送** | 不支持 | 原生支持 |

### 握手过程
1. **客户端请求升级**：发送包含`Upgrade: websocket`的HTTP请求
2. **服务器确认升级**：返回`101 Switching Protocols`状态码
3. **协议切换完成**：开始WebSocket通信

### 帧格式与操作码

#### 关键操作码
| 操作码 | 含义 | 说明 |
|--------|------|------|
| **0x1** | 文本帧 | UTF-8文本数据 |
| **0x2** | 二进制帧 | 二进制数据 |
| **0x8** | 关闭帧 | 关闭连接 |
| **0x9** | Ping帧 | 心跳检测 |
| **0xA** | Pong帧 | 心跳响应 |

### Go语言WebSocket实现

#### 基本服务器实现
```go
var upgrader = websocket.Upgrader{
    CheckOrigin: func(r *http.Request) bool {
        return true // 允许跨域
    },
}

func wsHandler(w http.ResponseWriter, r *http.Request) {
    conn, err := upgrader.Upgrade(w, r, nil)
    if err != nil {
        return
    }
    defer conn.Close()

    for {
        messageType, message, err := conn.ReadMessage()
        if err != nil {
            break
        }
        // 回显消息
        conn.WriteMessage(messageType, message)
    }
}
```

### 连接管理与心跳检测

#### 连接池设计
```go
type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
}

type Client struct {
    hub  *Hub
    conn *websocket.Conn
    send chan []byte
}
```

#### 心跳检测机制
- **Ping/Pong帧**：定期发送Ping帧，接收Pong响应
- **超时设置**：设置读写超时时间
- **连接状态监控**：及时清理断开的连接

### 应用场景与优化

#### 典型应用场景
- **即时通讯**：聊天室、私信系统
- **实时通知**：系统消息、状态更新
- **在线协作**：文档编辑、白板共享
- **实时监控**：系统监控、日志查看
- **在线游戏**：状态同步、实时对战

#### 技术选型对比
| 场景 | 推荐方案 | 原因 |
|------|----------|------|
| **简单推送** | Server-Sent Events | 单向推送，实现简单 |
| **双向实时通信** | WebSocket | 全双工，低延迟 |
| **高频数据更新** | WebSocket | 减少HTTP开销 |
| **偶尔推送** | HTTP轮询 | 实现简单，资源占用少 |

#### 性能优化策略
- **连接管理**：合理设置超时、连接池复用、及时清理
- **消息处理**：消息队列缓冲、批量处理、压缩优化
- **扩展性**：Redis发布订阅、负载均衡、水平扩展

### 面试要点

**Q: WebSocket和HTTP的主要区别？**
A:
1. **连接方式**：WebSocket长连接 vs HTTP短连接
2. **通信模式**：WebSocket全双工 vs HTTP请求-响应
3. **协议开销**：WebSocket握手后开销小，HTTP每次都有头部
4. **实时性**：WebSocket真正实时，HTTP需要轮询

**Q: WebSocket如何保持连接稳定？**
A:
1. **心跳检测**：定期发送Ping/Pong帧检测连接状态
2. **超时设置**：合理设置读写超时时间
3. **错误处理**：监听连接状态，断线自动重连
4. **资源管理**：及时清理断开的连接

**Q: WebSocket的安全性如何保证？**
A:
1. **WSS协议**：使用TLS加密传输数据
2. **Origin检查**：验证请求来源防止CSRF
3. **身份认证**：握手时验证用户身份
4. **消息验证**：对消息内容进行校验和过滤

**Q: 如何处理WebSocket高并发场景？**
A:
1. **连接池管理**：限制单机连接数，防止资源耗尽
2. **消息队列**：异步处理消息，提高吞吐量
3. **负载均衡**：分散连接到多台服务器
4. **资源监控**：实时监控内存、CPU使用情况