## 负载均衡算法与策略

### 核心概念
负载均衡将网络请求分发到多个服务器，提高可用性、性能和扩展性。

### 主要算法对比

| 算法 | 原理 | 优点 | 缺点 | 适用场景 |
|------|------|------|------|----------|
| **轮询** | 按顺序分配 | 简单均匀 | 不考虑性能差异 | 服务器性能相近 |
| **加权轮询** | 按权重分配 | 考虑性能差异 | 静态权重 | 性能差异固定 |
| **最少连接** | 选择连接数最少 | 动态负载感知 | 维护状态复杂 | 长连接应用 |
| **加权最少连接** | 连接数/权重最小 | 综合考虑性能负载 | 实现复杂 | 性能差异大且负载变化 |
| **IP哈希** | 基于客户端IP | 会话保持 | 可能负载不均 | 需要会话保持 |
| **最短响应时间** | 选择响应最快 | 用户体验最优 | 需要监控响应时间 | 响应时间敏感 |

### 负载均衡分层

#### 四层负载均衡（L4）
- **工作层次**：传输层（TCP/UDP）
- **特点**：基于IP和端口转发，性能高，延迟低
- **代表**：LVS、F5

#### 七层负载均衡（L7）
- **工作层次**：应用层（HTTP/HTTPS）
- **特点**：基于内容转发，支持复杂路由规则
- **代表**：Nginx、HAProxy

### 实现方式对比

| 类型 | 优点 | 缺点 | 代表产品 |
|------|------|------|----------|
| **DNS负载均衡** | 简单、分布式 | 缓存延迟、无健康检查 | DNS轮询 |
| **硬件负载均衡** | 性能强、功能丰富 | 成本高、扩展性差 | F5、A10 |
| **软件负载均衡** | 成本低、灵活 | 性能相对较低 | Nginx、HAProxy |

### 健康检查与会话保持

#### 健康检查方式
- **TCP检查**：检查端口连通性
- **HTTP检查**：发送HTTP请求验证响应
- **自定义检查**：调用业务接口检查状态

#### 故障处理流程
1. 检测到服务器故障
2. 自动摘除故障节点
3. 请求转发到健康服务器
4. 故障恢复后自动加入

#### 会话保持策略
| 策略 | 实现方式 | 优点 | 缺点 |
|------|----------|------|------|
| **IP绑定** | 基于IP哈希 | 简单 | 可能不均衡 |
| **Cookie绑定** | Cookie标记服务器 | 灵活 | 依赖客户端 |
| **Session复制** | 服务器间同步 | 可靠 | 网络开销大 |
| **集中存储** | Redis等外部存储 | 高可用 | 增加依赖 |

### 面试要点

**Q: 如何选择负载均衡算法？**
A: 根据应用特点选择：
- **无状态应用**：轮询、最少连接
- **有状态应用**：IP哈希、会话保持
- **性能差异大**：加权算法
- **响应时间敏感**：最短响应时间

**Q: 负载均衡器的高可用如何保证？**
A:
- **主备模式**：Keepalived + VIP漂移
- **集群模式**：多个负载均衡器协同工作
- **DNS轮询**：多个负载均衡器IP

**Q: 如何监控负载均衡效果？**
A:
- **负载分布**：各服务器请求分配情况
- **响应时间**：平均响应时间和P99延迟
- **可用性**：错误率和健康检查状态
- **吞吐量**：QPS和连接数统计

**Q: 四层和七层负载均衡的选择？**
A:
- **四层**：性能要求高，简单转发场景
- **七层**：需要基于内容路由，SSL终结，缓存等功能
