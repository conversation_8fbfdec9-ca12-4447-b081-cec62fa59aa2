## DNS域名解析过程

### DNS解析流程

DNS（Domain Name System）将域名转换为IP地址的分布式系统。

#### 解析流程
1. **缓存查询**：浏览器 → 系统 → 路由器 → ISP DNS
2. **递归查询**：本地DNS → 根服务器 → 顶级域服务器 → 权威服务器
3. **结果返回**：逐级返回并缓存结果

### DNS缓存层级

**优先级**：浏览器缓存 > 系统缓存 > 路由器缓存 > ISP DNS缓存

### DNS服务器层次结构

#### 服务器类型
| 服务器类型 | 作用 | 示例 |
|------------|------|------|
| **根域名服务器** | 顶级域指向 | 全球13组根服务器 |
| **顶级域服务器** | 二级域指向 | .com, .org, .cn |
| **权威DNS服务器** | 域名最终解析 | example.com的NS服务器 |
| **递归DNS服务器** | 代理查询 | *******, *************** |

### DNS记录类型

| 记录类型 | 作用 | 示例 |
|----------|------|------|
| **A** | 域名→IPv4 | www.example.com → ************* |
| **AAAA** | 域名→IPv6 | www.example.com → 2606:2800:... |
| **CNAME** | 域名别名 | blog.example.com → www.example.com |
| **MX** | 邮件服务器 | example.com → mail.example.com |
| **NS** | 域名服务器 | example.com → ns1.example.com |

### 查询类型

- **递归查询**：DNS服务器代为完成所有查询
- **迭代查询**：客户端依次查询各级服务器

### 性能优化

#### TTL设置
- **频繁变更**：300秒
- **稳定记录**：3600秒
- **CDN记录**：60秒

#### 其他优化
- **DNS预解析**：`<link rel="dns-prefetch">`
- **选择快速DNS**：*******、*******
- **CDN加速**：就近访问

### DNS安全

| 威胁 | 防护措施 |
|------|----------|
| **DNS劫持** | DNSSEC、DoH/DoT |
| **缓存投毒** | 可信DNS服务器 |
| **放大攻击** | 限制递归查询 |

### 面试要点

**Q: DNS解析的完整过程？**
A:
1. 检查浏览器缓存
2. 检查操作系统缓存
3. 查询本地DNS服务器
4. 递归查询：根服务器→顶级域服务器→权威服务器
5. 返回结果并缓存

**Q: 递归查询和迭代查询的区别？**
A:
- **递归查询**：DNS服务器代为完成所有查询，返回最终结果
- **迭代查询**：DNS服务器只返回下一步查询地址，客户端自己查询

**Q: 如何优化DNS解析性能？**
A:
1. **合理设置TTL**：平衡缓存效果和更新及时性
2. **DNS预解析**：提前解析可能用到的域名
3. **使用CDN**：就近访问，减少解析时间
4. **选择快速DNS服务器**：如*******、*******

**Q: DNS缓存的作用和问题？**
A:
- **作用**：提高解析速度，减少网络请求
- **问题**：可能返回过期数据，需要合理设置TTL

**Q: DNSSEC的作用？**
A: 通过数字签名验证DNS响应的真实性和完整性，防止DNS劫持和缓存投毒攻击