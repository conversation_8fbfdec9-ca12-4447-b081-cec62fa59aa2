## IPv4与IPv6对比

### 基本概念

#### IPv4 (Internet Protocol version 4)
- **发布时间**：1981年
- **地址长度**：32位
- **地址数量**：约43亿个(2^32)
- **表示方法**：点分十进制(***********)

#### IPv6 (Internet Protocol version 6)
- **发布时间**：1998年
- **地址长度**：128位
- **地址数量**：约3.4×10^38个(2^128)
- **表示方法**：冒号分十六进制(2001:db8::1)

### 主要差异对比

| 特性 | IPv4 | IPv6 |
|------|------|------|
| **地址长度** | 32位 | 128位 |
| **地址空间** | 43亿 | 3.4×10^38 |
| **头部长度** | 20-60字节(可变) | 40字节(固定) |
| **分片处理** | 路由器和主机 | 仅主机处理 |
| **校验和** | 有 | 无(由上层协议处理) |
| **配置方式** | 手动/DHCP | 自动配置/DHCPv6 |
| **广播** | 支持 | 不支持(用多播替代) |
| **安全性** | 可选(IPSec) | 内置(IPSec) |

### 地址结构对比

#### IPv4地址结构
- **网络部分**：标识网络
- **主机部分**：标识主机
- **子网掩码**：区分网络和主机部分
- **CIDR表示**：***********/24

#### IPv6地址结构
- **全球路由前缀**：48位，ISP分配
- **子网ID**：16位，组织内部子网
- **接口ID**：64位，主机标识
- **表示方法**：2001:db8:85a3::8a2e:370:7334

### IPv6地址类型

#### 1. 单播地址 (Unicast)
- **全球单播**：类似IPv4公网地址
- **链路本地**：fe80::/10，仅本地链路有效
- **唯一本地**：fc00::/7，类似IPv4私有地址
- **环回地址**：::1，类似IPv4的127.0.0.1

#### 2. 多播地址 (Multicast)
- **前缀**：ff00::/8
- **作用**：替代IPv4的广播功能
- **范围**：节点本地、链路本地、站点本地、全球

#### 3. 任播地址 (Anycast)
- **特点**：多个节点共享同一地址
- **路由**：数据包发送到最近的节点
- **应用**：负载均衡、就近服务

### IPv6的优势

#### 1. 地址空间巨大
- 解决IPv4地址耗尽问题
- 每个设备都可以有公网地址
- 简化网络架构，减少NAT需求

#### 2. 简化的头部结构
- 固定40字节头部长度
- 提高路由器处理效率
- 可选头部使用扩展头部

#### 3. 自动配置能力
- **无状态自动配置(SLAAC)**：基于路由器通告
- **有状态配置(DHCPv6)**：类似DHCP
- **重复地址检测(DAD)**：自动检测地址冲突

#### 4. 内置安全性
- IPSec成为标准组件
- 端到端加密和认证
- 减少中间人攻击风险

#### 5. 移动性支持
- 移动IPv6协议
- 无缝切换网络
- 保持连接状态

### IPv4到IPv6的过渡技术

#### 1. 双栈技术 (Dual Stack)
- **原理**：同时运行IPv4和IPv6协议栈
- **优点**：兼容性好，逐步迁移
- **缺点**：资源消耗大，管理复杂

#### 2. 隧道技术 (Tunneling)
- **6to4隧道**：通过IPv4网络传输IPv6
- **Teredo隧道**：穿越NAT的IPv6隧道
- **ISATAP**：站点内自动隧道

#### 3. 地址转换 (Translation)
- **NAT64**：IPv6到IPv4的网络地址转换
- **DNS64**：DNS查询转换
- **464XLAT**：移动网络转换方案

### 部署挑战

#### 技术挑战
- **应用程序兼容性**：需要更新支持IPv6
- **网络设备升级**：路由器、交换机、防火墙
- **运维人员培训**：新的地址格式和配置方法

#### 商业挑战
- **投资成本**：设备升级和人员培训
- **过渡期复杂性**：双栈运行增加管理负担
- **用户体验**：确保服务不中断

### 当前部署状况

#### 全球部署情况
- **Google统计**：全球IPv6流量占比约35%
- **区域差异**：欧洲和亚洲部署较快
- **运营商推动**：移动网络IPv6部署领先

#### 中国部署情况
- **政策推动**：国家大力推进IPv6部署
- **运营商网络**：三大运营商全面支持IPv6
- **内容提供商**：主要网站和应用支持IPv6

### 面试要点

**Q: IPv4和IPv6的主要区别是什么？**
A:
- **地址长度**：IPv4为32位，IPv6为128位
- **地址空间**：IPv6解决了IPv4地址不足问题
- **头部结构**：IPv6头部固定40字节，更简洁
- **安全性**：IPv6内置IPSec支持
- **配置**：IPv6支持自动配置

**Q: 为什么需要从IPv4迁移到IPv6？**
A:
- **地址耗尽**：IPv4地址已基本分配完毕
- **NAT问题**：IPv4的NAT破坏了端到端连接
- **扩展性**：IPv6提供更好的扩展性和性能
- **安全性**：IPv6内置安全机制
- **移动性**：更好的移动设备支持

**Q: IPv4到IPv6的过渡技术有哪些？**
A:
- **双栈**：同时运行IPv4和IPv6
- **隧道**：6to4、Teredo、ISATAP等
- **转换**：NAT64、DNS64等
- **代理**：应用层代理转换

**Q: IPv6地址有哪些类型？**
A:
- **单播**：一对一通信，包括全球单播、链路本地、唯一本地
- **多播**：一对多通信，替代IPv4广播
- **任播**：一对最近通信，用于负载均衡

**Q: IPv6部署面临哪些挑战？**
A:
- **兼容性**：应用程序和设备需要升级
- **复杂性**：过渡期需要维护双栈
- **成本**：设备升级和人员培训成本
- **管理**：新的地址格式和配置方法
