## TCP滑动窗口与流量控制

### 滑动窗口机制

#### 基本概念
**滑动窗口**是TCP实现流量控制和可靠传输的核心机制，允许发送方在收到确认前发送多个数据包。

#### 窗口类型
1. **发送窗口**：发送方可以发送但未确认的数据范围
2. **接收窗口**：接收方可以接收的数据范围
3. **拥塞窗口**：网络拥塞控制的窗口大小

### 发送窗口结构

**发送缓冲区分为四个区域**：
- **区域1**：已发送且已确认的数据
- **区域2**：已发送但未确认的数据
- **区域3**：可以发送的数据
- **区域4**：不能发送的数据（超出窗口）

### 接收窗口结构

**接收缓冲区分为三个区域**：
- **区域1**：已接收已确认的数据
- **区域2**：可接收的数据
- **区域3**：不可接收的数据（超出窗口）

### 窗口滑动过程

#### 滑动机制
1. **发送方发送数据包**：在窗口范围内发送数据
2. **接收方确认接收**：发送ACK确认序号
3. **发送窗口滑动**：收到ACK时向右滑动
4. **接收窗口滑动**：应用程序读取数据时向右滑动

#### 窗口更新
- **发送窗口**：收到ACK时向右滑动
- **接收窗口**：应用程序读取数据时向右滑动
- **窗口大小**：由接收方的rwnd字段通告

### 流量控制机制

#### 接收方控制
- **接收窗口大小** = 接收缓冲区大小 - 已接收未读取的数据量
- **发送方实际窗口** = min(拥塞窗口, 接收窗口)

#### 零窗口问题
**问题**：接收方缓冲区满，通告窗口为0
**解决**：
1. **窗口探测**：发送方定期发送1字节数据探测
2. **窗口更新**：接收方缓冲区有空间时发送窗口更新

### 窗口缩放选项

#### 问题背景
- 原始TCP窗口字段只有16位（最大65535字节）
- 高带宽网络需要更大的窗口

#### 解决方案
- **窗口缩放选项**：在TCP选项中协商缩放因子
- **计算公式**：实际窗口大小 = 窗口字段值 × 2^缩放因子
- **最大窗口**：65535 × 2^14 = 1GB

### 性能影响因素

#### 带宽延迟积（BDP）
- **计算公式**：BDP = 带宽 × 往返时延
- **窗口设置**：理想窗口大小 ≥ BDP
- **示例**：100Mbps × 100ms = 1.25MB

#### 窗口大小设置
- **太小**：无法充分利用带宽
- **太大**：浪费内存，增加重传延迟

### 实际应用

#### Linux TCP参数调优
- `net.core.rmem_max`：最大接收缓冲区
- `net.ipv4.tcp_rmem`：TCP接收缓冲区范围
- `net.core.wmem_max`：最大发送缓冲区
- `net.ipv4.tcp_wmem`：TCP发送缓冲区范围
- `net.ipv4.tcp_window_scaling`：启用窗口缩放

#### Go程序中的设置
- `SetReadBuffer()`：设置接收缓冲区大小
- `SetWriteBuffer()`：设置发送缓冲区大小

### 常见问题

#### 1. 窗口收缩
**问题**：接收方减小窗口大小
**影响**：可能导致数据丢失
**解决**：避免窗口收缩，合理设置缓冲区

#### 2. Silly Window Syndrome
**问题**：窗口太小，频繁发送小数据包
**解决**：
- **接收方**：延迟窗口更新直到有足够空间
- **发送方**：累积数据到一定大小再发送

#### 3. 窗口探测超时
**问题**：零窗口时探测包丢失
**解决**：指数退避重传探测包

### 与拥塞控制的关系

#### 窗口大小决定
```
发送窗口 = min(拥塞窗口, 接收窗口)
```

#### 不同阶段的控制
- **慢启动**：拥塞窗口指数增长
- **拥塞避免**：拥塞窗口线性增长
- **快速恢复**：快速调整拥塞窗口

### 重传机制

#### 超时重传
- **RTO计算**：基于RTT和RTTVAR动态计算
- **指数退避**：连续超时时RTO翻倍
- **重传队列**：维护未确认的数据包

#### 快速重传
```
接收方收到乱序包 → 发送DupACK
发送方收到3个DupACK → 立即重传
```

#### 选择性确认(SACK)
- **SACK选项**：告知已接收的非连续数据段
- **精确重传**：只重传真正丢失的数据
- **提高效率**：减少不必要的重传

### 面试要点

**Q: 滑动窗口的作用是什么？**
A:
1. **流量控制**：防止发送方发送过快导致接收方缓冲区溢出
2. **提高效率**：允许发送多个数据包而不等待每个ACK
3. **可靠传输**：配合序列号实现数据的有序可靠传输

**Q: 零窗口问题如何解决？**
A:
1. **窗口探测**：发送方定期发送1字节探测包
2. **窗口更新**：接收方有空间时主动发送窗口更新
3. **合理设置缓冲区大小**

**Q: TCP如何保证可靠传输？**
A:
1. **序列号**：保证数据有序
2. **确认机制**：ACK确认接收
3. **重传机制**：超时重传+快速重传
4. **流量控制**：滑动窗口防止溢出
5. **拥塞控制**：防止网络拥塞

**Q: 快速重传的触发条件？**
A: 发送方连续收到3个相同的DupACK时立即重传，不等待超时

**Q: 滑动窗口和拥塞控制的区别？**
A:
- **滑动窗口**：端到端的流量控制，防止接收方过载
- **拥塞控制**：网络层面的控制，防止网络拥塞
- **实际窗口**：取两者的最小值
