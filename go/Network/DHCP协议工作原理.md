## DHCP协议工作原理

### DHCP基本概念

**定义**：动态主机配置协议(Dynamic Host Configuration Protocol)，自动为网络设备分配IP地址和网络配置参数。

**作用**：
- 自动分配IP地址
- 配置子网掩码、网关、DNS
- 简化网络管理
- 避免IP地址冲突

### DHCP工作流程

#### 四步握手过程 (DORA)

**1. Discover (发现)**
- 客户端广播DHCP Discover消息
- 寻找可用的DHCP服务器
- 使用0.0.0.0作为源地址，***************作为目标地址

**2. Offer (提供)**
- DHCP服务器响应DHCP Offer消息
- 提供可用的IP地址和配置参数
- 包含租约时间等信息

**3. Request (请求)**
- 客户端选择一个Offer，发送DHCP Request
- 正式请求分配该IP地址
- 拒绝其他服务器的Offer

**4. Acknowledge (确认)**
- 服务器发送DHCP ACK确认分配
- 客户端开始使用分配的IP地址
- 建立租约关系

### DHCP消息类型

| 消息类型 | 方向 | 作用 |
|----------|------|------|
| **DISCOVER** | 客户端→服务器 | 寻找DHCP服务器 |
| **OFFER** | 服务器→客户端 | 提供IP地址 |
| **REQUEST** | 客户端→服务器 | 请求IP地址 |
| **ACK** | 服务器→客户端 | 确认分配 |
| **NAK** | 服务器→客户端 | 拒绝请求 |
| **RELEASE** | 客户端→服务器 | 释放IP地址 |
| **RENEW** | 客户端→服务器 | 续约请求 |
| **INFORM** | 客户端→服务器 | 请求配置信息 |

### DHCP租约管理

#### 租约生命周期
1. **分配阶段**：通过DORA过程获得IP地址
2. **使用阶段**：客户端正常使用分配的地址
3. **续约阶段**：租约到期前自动续约
4. **释放阶段**：主动释放或租约过期

#### 租约时间配置
- **默认租约时间**：通常为24小时
- **最大租约时间**：服务器允许的最长时间
- **续约时间点**：租约时间的50%时开始尝试续约
- **重绑定时间点**：租约时间的87.5%时重新绑定

### DHCP配置参数

#### 基本网络参数
- **IP地址**：分配给客户端的IP地址
- **子网掩码**：网络掩码
- **默认网关**：路由器地址
- **DNS服务器**：域名解析服务器

#### 高级配置选项
- **域名**：客户端所属域名
- **NTP服务器**：时间同步服务器
- **WINS服务器**：NetBIOS名称服务器
- **启动文件**：网络启动文件路径

### DHCP部署模式

#### 1. 单服务器模式
- **特点**：一台DHCP服务器服务整个网络
- **优点**：配置简单，管理集中
- **缺点**：单点故障风险
- **适用**：小型网络环境

#### 2. 多服务器模式
- **特点**：多台服务器提供冗余
- **配置**：80/20规则分配地址池
- **优点**：高可用性
- **缺点**：配置复杂，可能冲突

#### 3. DHCP中继模式
- **特点**：通过中继代理转发DHCP消息
- **作用**：跨子网提供DHCP服务
- **实现**：路由器或专用中继设备
- **优点**：集中管理，减少服务器数量

### DHCP安全考虑

#### 安全威胁
- **DHCP欺骗**：恶意DHCP服务器提供错误配置
- **地址耗尽攻击**：恶意请求耗尽地址池
- **中间人攻击**：通过错误网关拦截流量
- **信息泄露**：DHCP消息包含网络拓扑信息

#### 安全防护措施
- **DHCP Snooping**：交换机过滤非法DHCP消息
- **端口安全**：限制端口MAC地址数量
- **VLAN隔离**：不同网段使用不同VLAN
- **访问控制**：基于MAC地址的访问控制

### 故障排除

#### 常见问题
- **无法获取IP地址**：DHCP服务器不可达或地址池耗尽
- **IP地址冲突**：静态IP与DHCP分配冲突
- **网络配置错误**：DNS、网关配置错误
- **租约问题**：租约过期或续约失败

#### 排查方法
- **检查DHCP服务状态**：确认服务器正常运行
- **查看地址池使用情况**：检查是否有可用地址
- **网络连通性测试**：ping测试网络连接
- **抓包分析**：分析DHCP消息交互过程

### 面试要点

**Q: DHCP的工作流程是什么？**
A: DHCP使用四步握手过程(DORA)：
1. **Discover**: 客户端广播寻找DHCP服务器
2. **Offer**: 服务器提供IP地址和配置
3. **Request**: 客户端请求特定IP地址
4. **Acknowledge**: 服务器确认分配

**Q: DHCP租约是什么？如何管理？**
A: 租约是IP地址的使用期限。管理包括：
- 设置合适的租约时间
- 自动续约机制(50%时间点)
- 重绑定机制(87.5%时间点)
- 租约释放和回收

**Q: 如何实现DHCP的高可用性？**
A:
- **多服务器部署**: 80/20规则分配地址池
- **DHCP中继**: 集中管理，跨子网服务
- **故障转移**: 主备服务器自动切换
- **负载均衡**: 分散DHCP请求负载

**Q: DHCP有哪些安全风险？如何防护？**
A:
- **风险**: DHCP欺骗、地址耗尽攻击、中间人攻击
- **防护**: DHCP Snooping、端口安全、VLAN隔离、MAC地址过滤

**Q: 如何排查DHCP问题？**
A:
1. 检查DHCP服务器状态和日志
2. 验证网络连通性
3. 查看地址池使用情况
4. 抓包分析DHCP消息交互
5. 检查客户端网络配置
