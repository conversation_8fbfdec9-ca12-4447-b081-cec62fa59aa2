## TCP拥塞控制算法

### 基础机制

#### 1. 慢启动（Slow Start）
- **目的**：探测可用网络带宽
- **机制**：cwnd指数级增长，每收到一个ACK就增加
- **结束条件**：达到ssthresh阈值，进入拥塞避免

#### 2. 拥塞避免（Congestion Avoidance）
- **目的**：避免网络拥塞
- **机制**：cwnd线性增长，每个RTT增加一个MSS
- **触发条件**：cwnd达到ssthresh时启动

#### 3. 快速重传（Fast Retransmit）
- **目的**：快速检测并重传丢失的数据包
- **机制**：收到3个重复ACK立即重传
- **优势**：不等待超时，减少延迟

#### 4. 快速恢复（Fast Recovery）
- **目的**：避免慢启动的性能损失
- **机制**：cwnd减半而非重置为1，直接进入拥塞避免
- **触发条件**：快速重传后启动

### 主要算法

#### TCP Reno
- **特点**：结合慢启动、拥塞避免、快速重传、快速恢复
- **优势**：快速响应网络拥塞
- **缺点**：高丢包率环境性能差

#### TCP NewReno
- **改进**：优化快速恢复阶段
- **特点**：处理多重丢包更高效
- **机制**：部分ACK机制，避免过度依赖慢启动

#### TCP Vegas
- **类型**：基于延迟的拥塞控制
- **机制**：监控RTT变化预测拥塞
- **优势**：主动避免拥塞，而非被动响应

#### CUBIC
- **地位**：Linux默认拥塞控制算法
- **适用**：高带宽、高延迟网络
- **机制**：三次曲线函数控制窗口增长
- **优势**：快速利用可用带宽

#### BBR (Bottleneck Bandwidth and RTT)
- **开发者**：Google
- **创新**：不依赖丢包作为拥塞信号
- **机制**：实时估算瓶颈带宽和RTT
- **优势**：最大化带宽利用率，最小化延迟

#### Compound TCP
- **类型**：混合算法
- **结合**：丢包检测 + 延迟检测
- **适用**：高带宽延迟产品(BDP)网络

### 面试要点

**Q: 慢启动和拥塞避免的区别？**
A:
- **慢启动**：指数增长，快速探测带宽
- **拥塞避免**：线性增长，避免网络过载

**Q: 快速重传的触发条件？**
A: 收到3个重复ACK，立即重传而不等待超时

**Q: BBR相比传统算法的优势？**
A: 不依赖丢包信号，主动控制流量，减少延迟波动

**Q: 如何选择拥塞控制算法？**
A:
- **高带宽长延迟**：CUBIC、BBR
- **低延迟要求**：Vegas、BBR
- **通用场景**：Reno、NewReno