## TCP半连接状态详解

### 半连接基本概念

**定义**：TCP半连接(Half-Open Connection)是指在三次握手过程中，服务器已发送SYN+ACK但未收到客户端ACK确认的连接状态。

**状态标识**：SYN_RCVD (SYN Received)

### 半连接产生场景

#### 1. 正常网络延迟
- **场景**：网络拥塞导致ACK包延迟
- **特点**：短暂的半连接状态，最终会正常建立
- **处理**：服务器等待超时后重传SYN+ACK

#### 2. 客户端异常
- **场景**：客户端程序崩溃或网络中断
- **特点**：客户端无法发送ACK包
- **结果**：服务器超时后释放半连接资源

#### 3. 网络故障
- **场景**：路由故障、防火墙阻断
- **特点**：ACK包无法到达服务器
- **影响**：连接建立失败，资源浪费

#### 4. SYN Flood攻击
- **场景**：恶意攻击者伪造大量SYN请求
- **特点**：故意不发送ACK包
- **危害**：耗尽服务器连接资源

### 半连接队列管理

#### 队列机制
- **半连接队列**：存储SYN_RCVD状态的连接
- **队列大小**：由tcp_max_syn_backlog参数控制
- **队列满时**：新的SYN请求被丢弃

#### 超时处理
- **重传机制**：服务器重传SYN+ACK包
- **重传次数**：由tcp_synack_retries参数控制
- **超时释放**：达到重传上限后释放资源

### 半连接攻击防护

#### 1. SYN Cookies技术
- **原理**：不为半连接分配资源
- **实现**：将连接信息编码到序列号中
- **优势**：有效防止资源耗尽攻击

#### 2. 系统参数调优
```bash
# 启用SYN Cookies
net.ipv4.tcp_syncookies = 1

# 调整半连接队列大小
net.ipv4.tcp_max_syn_backlog = 2048

# 减少重传次数
net.ipv4.tcp_synack_retries = 2
```

#### 3. 应用层防护
- **连接限制**：限制单IP并发连接数
- **频率控制**：限制SYN请求频率
- **黑名单机制**：阻断异常IP地址

### 监控与诊断

#### 监控命令
```bash
# 查看半连接数量
netstat -an | grep SYN_RECV | wc -l

# 查看连接状态统计
ss -ant | awk '{print $1}' | sort | uniq -c

# 查看TCP统计信息
cat /proc/net/netstat | grep TcpExt
```

#### 关键指标
- **SYN_RECV连接数**：正常<100，异常>1000
- **连接建立成功率**：正常>99%，攻击时<50%
- **平均响应时间**：正常<100ms，攻击时>5s

### 面试要点

**Q: 什么是TCP半连接？**
A: TCP半连接是三次握手过程中服务器发送SYN+ACK后等待客户端ACK确认的状态，连接处于SYN_RCVD状态。

**Q: 半连接状态可能由什么原因产生？**
A:
- 网络延迟或拥塞导致ACK包延迟
- 客户端程序异常无法发送ACK
- 网络故障阻断ACK包传输
- SYN Flood攻击故意不发送ACK

**Q: 如何防护半连接攻击？**
A:
- **SYN Cookies**：不预分配资源，用cookie验证连接
- **参数调优**：调整队列大小和超时时间
- **应用防护**：连接限制、频率控制、IP黑名单
- **详细防护**：参见"什么是 SYN flood.md"

**Q: 如何监控半连接状态？**
A: 使用netstat、ss等命令查看SYN_RECV状态连接数，监控连接建立成功率和响应时间等指标。