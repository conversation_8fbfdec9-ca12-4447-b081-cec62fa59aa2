## 网络分层模型实际应用

### 数据包处理流程

#### 封装过程（发送）
**应用层** → **传输层** → **网络层** → **数据链路层** → **物理层**
逐层添加头部信息，最终转换为物理信号传输

#### 解封过程（接收）
**物理层** → **数据链路层** → **网络层** → **传输层** → **应用层**
逐层解析头部信息，最终交给应用程序处理

### 各层关键协议

| 层次 | 主要协议 | 功能 |
|------|----------|------|
| **应用层** | HTTP/HTTPS, FTP, SMTP, DNS, SSH | 应用程序间通信 |
| **传输层** | TCP, UDP | 端到端数据传输 |
| **网络层** | IP, ICMP | 路由和寻址 |
| **数据链路层** | 以太网, WiFi | 局域网内数据传输 |
| **物理层** | 电缆, 光纤, 无线 | 物理信号传输 |

#### 协议特点对比
- **TCP**：可靠、面向连接、适用于Web、邮件
- **UDP**：快速、无连接、适用于DNS、视频
- **IP**：寻址路由、IPv4/IPv6
- **以太网**：局域网标准、MAC地址

### 网络设备与分层

| 设备 | 工作层次 | 主要功能 |
|------|----------|----------|
| **集线器** | 物理层 | 信号放大转发 |
| **交换机** | 数据链路层 | MAC地址转发 |
| **路由器** | 网络层 | IP路由选择 |
| **防火墙** | 网络-应用层 | 安全过滤 |

### 故障排查方法

#### 分层排查
1. **物理层**：检查网线、网卡状态
2. **数据链路层**：检查MAC地址、VLAN
3. **网络层**：ping测试、路由检查
4. **传输层**：端口连通性测试
5. **应用层**：协议特定测试

#### 常用工具
```bash
ping *******          # 连通性测试
traceroute google.com  # 路由跟踪
netstat -tuln         # 端口监听
curl -v http://url     # HTTP测试
```

### 面试要点

**Q: 数据包在网络中是如何传输的？**
A: 
1. 应用层生成数据，逐层添加头部信息
2. 物理层转换为信号传输
3. 接收端逐层解析，去除头部信息
4. 最终到达应用层处理

**Q: 为什么需要网络分层？**
A:
1. **模块化**：每层专注特定功能
2. **标准化**：统一接口和协议
3. **可维护性**：修改一层不影响其他层
4. **互操作性**：不同厂商设备可以互通

**Q: 如何排查网络问题？**
A: 按分层模型从下往上排查：
1. 物理连接 → 2. 数据链路 → 3. 网络连通 → 4. 端口服务 → 5. 应用协议

**Q: 不同网络设备工作在哪一层？**
A: 集线器(物理层) → 交换机(数据链路层) → 路由器(网络层) → 防火墙/负载均衡器(传输层以上)
