## 网络I/O多路复用详解

### I/O多路复用概述

**定义**：单个进程监控多个文件描述符，当某个描述符就绪时进行相应的I/O操作。

**核心思想**：
- 用一个线程处理多个连接
- 避免为每个连接创建线程的开销
- 提高服务器并发处理能力

**适用场景**：
- 高并发服务器
- 需要同时处理多个网络连接
- I/O密集型应用

### 三种实现方式

#### 1. select

**工作原理**：
- 监控文件描述符集合
- 轮询检查描述符状态
- 返回就绪的描述符数量

**特点**：
- 跨平台支持好
- 文件描述符数量有限制（通常1024）
- 每次调用需要重新设置描述符集合
- 时间复杂度O(n)

**使用场景**：
- 连接数较少的应用
- 需要跨平台兼容性

#### 2. poll

**工作原理**：
- 使用pollfd结构体数组
- 轮询检查每个描述符状态
- 返回就绪的描述符数量

**特点**：
- 没有文件描述符数量限制
- 不需要重新设置描述符集合
- 时间复杂度O(n)
- Linux特有

**优势**：
- 解决了select的描述符数量限制
- 接口更简洁

#### 3. epoll

**工作原理**：
- 基于事件驱动
- 内核维护就绪列表
- 只返回就绪的描述符

**特点**：
- 时间复杂度O(1)
- 支持边缘触发(ET)和水平触发(LT)
- 没有描述符数量限制
- Linux特有

**优势**：
- 性能最高
- 内存拷贝少
- 支持大量并发连接

### select vs poll vs epoll对比

| 特性 | select | poll | epoll |
|------|--------|------|-------|
| **性能** | O(n) | O(n) | O(1) |
| **描述符限制** | 1024 | 无限制 | 无限制 |
| **内存拷贝** | 每次都拷贝 | 每次都拷贝 | 只拷贝就绪的 |
| **跨平台** | 是 | 否 | 否 |
| **触发方式** | 水平触发 | 水平触发 | 水平/边缘触发 |

### epoll详解

#### 工作模式

**水平触发(LT - Level Triggered)**：
- 默认模式
- 只要描述符就绪就会通知
- 编程简单，不容易出错
- 类似select和poll的行为

**边缘触发(ET - Edge Triggered)**：
- 只在状态变化时通知
- 性能更高，减少系统调用
- 编程复杂，需要处理EAGAIN
- 必须使用非阻塞I/O

#### 核心API
- **epoll_create1()**：创建epoll实例
- **epoll_ctl()**：添加/修改/删除监控的描述符
- **epoll_wait()**：等待事件发生，返回就绪的描述符

#### 事件类型
- **EPOLLIN**：可读事件
- **EPOLLOUT**：可写事件
- **EPOLLERR**：错误事件
- **EPOLLHUP**：挂起事件
- **EPOLLET**：边缘触发模式

### 实际应用场景

#### Web服务器
- **Nginx**：使用epoll处理大量并发连接
- **Apache**：传统多进程/多线程模型
- **Node.js**：基于事件循环的异步I/O

#### 数据库
- **Redis**：单线程 + epoll多路复用
- **MySQL**：多线程 + 阻塞I/O

#### 消息队列
- **Kafka**：NIO + epoll
- **RabbitMQ**：Erlang VM + epoll

### 编程模型

#### Reactor模式
1. 注册事件处理器到Reactor
2. Reactor监听事件
3. 事件发生时调用对应处理器
4. 处理器处理事件并返回

#### 事件循环
- 循环调用epoll_wait等待事件
- 遍历返回的就绪事件
- 根据事件类型调用相应处理函数
- 处理完成后继续等待下一批事件

### 性能优化技巧

#### 1. 减少系统调用
- 批量处理事件
- 使用边缘触发模式
- 合理设置超时时间

#### 2. 内存管理
- 预分配事件数组
- 复用连接对象
- 避免频繁内存分配

#### 3. 线程模型
- 主线程处理连接接受
- 工作线程处理I/O事件
- 避免线程间频繁切换

### 常见问题与解决

#### 1. 惊群问题
- **问题**：多个进程/线程同时监听同一个socket
- **解决**：使用SO_REUSEPORT或文件锁

#### 2. 内存泄漏
- **问题**：忘记关闭文件描述符
- **解决**：及时调用close()和epoll_ctl删除

#### 3. 事件丢失
- **问题**：边缘触发模式下未完全处理数据
- **解决**：循环读取直到EAGAIN

### 面试要点

**Q: 什么是I/O多路复用？**
A: 单个进程监控多个文件描述符，当某个描述符就绪时进行相应的I/O操作，避免为每个连接创建线程的开销。

**Q: select、poll、epoll的区别？**
A:
- **select**：有描述符数量限制，O(n)复杂度，跨平台
- **poll**：无描述符限制，O(n)复杂度，Linux特有
- **epoll**：无描述符限制，O(1)复杂度，性能最高

**Q: epoll的水平触发和边缘触发区别？**
A:
- **水平触发**：只要描述符就绪就通知，编程简单
- **边缘触发**：只在状态变化时通知，性能更高但编程复杂

**Q: 为什么epoll性能比select好？**
A:
- 时间复杂度O(1) vs O(n)
- 只返回就绪的描述符，减少遍历
- 内核维护就绪列表，减少内存拷贝
- 支持大量并发连接

**Q: I/O多路复用适用于什么场景？**
A: 高并发服务器、需要同时处理多个网络连接、I/O密集型应用，如Web服务器、代理服务器、聊天服务器等。
