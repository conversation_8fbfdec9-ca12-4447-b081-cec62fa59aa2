# AI 网文产线核心方法论

> 本文档旨在系统性阐述搭建AI网文产线的核心方法、技术架构与商业思考，将原有问答内容重组为更具逻辑性的结构。

---

## 一、 核心理念与整体流程

### 问：搭建AI网文产线的核心思路和整体业务流程是怎样的？

> **核心思路**：我们的核心思路根植于对成熟网文作者创作模式的深度模拟。因为AI写作与人写作面临的很多本质问题是相通的——都需要通过持续的外部灵感输入来创新，都需要设计精巧的结构，去铺设期待、引爆爽点。我们观察到，优秀作者普遍通过"扫榜拆文"的方式，研究爆款作品的写作理论。因此，我们确立了"**对标爆款 + 收集素材库 + AI生产大纲、正文 + 人机精修**"的模式，将经过市场验证的创作方法论和爆款案例，系统性地赋予AI，以实现网文的规模化、高质量生产。
>
> **业务流程**：
>
> 1.  **选品与素材库建设**：此阶段是生产线的源头。我们通过数据系统，持续监控并采集**七猫、番茄、阅文**等主流平台的**畅销作品**与**新锐作品**。这些爆款书有两个核心用途：
>     *   **拆解为素材**：深度拆解作品，构建包含**剧情单元、人物设定、开篇切入点、"金句"语料**的结构化素材库。我们甚至会分析高赞"**段评**"（段落评论），挖掘读者真正产生共鸣的"热门语料"。
>     *   **学习其骨架**：分析作品的**大纲排布节奏和框架**，学习其如何在不同阶段铺设期待感、爆发爽点，以及如何组织剧情来驱动情绪。
> 2.  **AI自动化生产**：
>     *   **选定赛道与母本**：首先，基于站内的类目分析，确定优先生产的题材赛道。然后，在赛道内选择一本逻辑框架清晰、经过市场验证的爆款书作为"母本"（Template），以确立故事的**核心节奏**。
>     *   **开篇创作**：开篇（约2-3万字）是留住读者的关键，重要性堪比短视频的前三秒。我们采取"**像素级模仿核心梗，细节处填充创新**"的策略，此阶段由人机结合完成。
>         *   **模仿核心梗**："核心梗"是抽象的情绪框架和情节模型。例如，一个文娱文开篇的"核心梗"可能是：`主角被女友羞辱分手 -> 独自参会时意外获得登台机会 -> 惊艳全场并结识新女主 -> 在新女主的帮助下回头打脸前女友`。我们严格遵循这个能引发强烈情绪起伏的框架。
>         *   **填充式创新**：在"核心梗"的框架内，我们融合**近期热点**和**新颖元素**进行创新。例如，"分手"的具体原因可以结合"天价彩礼"等社会热点；"登台"的方式可以有多种设计，可能是点歌上台或者演唱互动上台；人物设定上也可以引入"病娇小富婆"等流行元素，实现"旧瓶装新酒"。
>     *   **大纲与剧情生成**：开篇完成后，主角通常已"走出新手村"，进入小有名气的稳定发展期。后续剧情的推进，我们会遵循母本的**主线节奏**，填充下一阶段，从小有名气到三线歌手。但在填充具体情节时，我们会采用一套精细化的剧情融合流程：
>         1. **智能召回与匹配**：我们通过**语义召回**技术，根据母本故事线的需求（如"主角需要一个打脸反派的剧情"），从素材库中初步筛选剧情单元。
>         2. **匹配度评估**：AI Agent会对候选剧情进行二次筛选，严格评估其与当前上下文的**匹配度**，包括**剧情影响**（该剧情能否达成母本要求的阶段性目标）、**角色设定**及**场景**是否与新书冲突。
>         3. **AI驱动改编**：选定最合适的剧情单元后，我们会将新书的世界观、人物状态等信息一并提供给大模型，由其自动完成**适应性改编**，将外部剧情无缝地融入当前故事线。对于逻辑上强关联的剧情（如比赛的初赛、复赛），我们会作为"剧情链"进行整体调用，保证其连贯性。
>     *   **正文写作**：融合世界观、人设、剧情梗概等信息，由AI生成2-3个版本、强调"电影镜头感"的正文初稿供选择。
> 3.  **人机结合质检精修**：我们建立"**AI初检+人工分级精修**"的质检流程。
>     *   **初级精修**：由外部协作者完成。AI工具会先自动扫描并标记出错别字、指代不清、前后矛盾等基础问题，生成一份"问题报告"，然后由人工进行二次校对修正，保证基础的阅读流畅性。
>     *   **高级精修**：由内部核心编辑负责，尤其针对信息密度大、决定作品成败的开篇。编辑会从节奏、伏笔、钩子等方面进行优化，并利用我们的"**语料库**"，将AI不擅长创作的、从"段评"中挖掘出的"梗"，在合适的场景融入正文，提升作品的趣味性和表现力。
> 4.  **有声化与商业验证**：根据作品质量，我们采用**数据驱动**的分级有声化策略进行商业验证。
>     *   **第一级 (TTS)**：首先用成本最低的**TTS**（文本转语音）制作成有声书专辑，上线试水。
>     *   **第二级 (AI制作人)**：若作品在一周内数据表现良好（如播放量达标），则升级，交由**AI制作人**使用克隆音色结合多角色配音进行精良制作。
>     *   **第三级 (真人主播)**：若AI制作人的版本再次获得优异的市场反馈，我们会投入最高成本，聘请**真人主播**进行顶级录制，最大化作品价值。

---

## 二、 内容生产策略

### 问：如何进行选题和创新？

> **选题策略**：核心依然是"**对标爆款**"。我们持续监听**七猫、番茄、阅文**等主流平台的新晋爆款和常青畅销书，并结合内部数据分析，决定优先跟进的品类与作品，确保我们的创作紧跟市场热点，拥有坚实的商业下限。
>
> **创新策略**：我们遵循"**八二原则**"下的"**微创新**"与"**元素融合**"。我们认为，除了少数大神级作品，多数网文都在特定题材的"套路"框架内。因此，我们的创新不会脱离这个边界，而是**八成遵循题材惯例，两成进行元素创新**。
> *   **创新元素来源**：我们的"**创新元素库**"完全来自于对最新爆款书的拆解。我们会提取其中新颖且受欢迎的**人物设定**（如女主的职业、性格）、**开篇情节桥段**等。
> *   **融合方式**：创作时，我们遵循母本的核心框架节奏，但会从创新库中挑选时下最受欢迎的元素进行融合替换，实现"**旧瓶装新酒**"的效果，即在成熟的商业框架内做精准、局部的创新。

### 问：什么是剧情单元化？为什么它对AI写作至关重要？

> **剧情单元化**是我为AI规模化生产提出的核心支撑概念，没有它，规模化便无从谈起。
>
> *   **概念与观察**：我观察到，当代网文为适应短视频时代的阅读习惯，其结构已趋向**模块化**。故事由一个个相对独立的"**剧情单元**"构成，每个单元都有完整的起承转合和情绪爽点。单元间的连接相对灵活（如主角爆火后，可以去录歌，也可以去参加综艺），甚至在同一发展阶段，不同剧情单元可以相互替换。这种模式降低了作者的创作负担和读者的阅读门槛，与《诡秘之主》那种层层嵌套、环环相扣的强逻辑结构形成对比。
> *   **生成流程**：这是一个**自下而上**的聚合过程。
>     1.  **章节拆解**：首先将一个章节（约2000字）拆解为10-15个更细粒度的"**情节点**"。
>     2.  **单元聚合**：由大模型分析并聚合语义相关的"情节点"，形成一个逻辑自洽、包含冲突与结局的"**剧情单元**"（通常对应2-3万字内容）。
>     3.  **剧情串链**：对于逻辑上强关联、不能拆分的单元（如综艺比赛的初赛、复赛、决赛），我们会将它们串联成"**剧情链**"进行存储。
> *   **核心优势**：
>     1.  **适配AI**：单元的体量（2-3万字）是AI能够很好理解和处理的范围。
>     2.  **化繁为简**：将创作几十上百万字长篇的复杂任务，降维为对"剧情单元"进行选择、改编和序列化排布的简单任务。
>     3.  **降低门槛**：这种模块化的思路极大地降低了内容生产的门槛，是实现规模化和未来工具化的关键。

### 问：如何利用素材库支撑内容创作？

> 我们的素材库主要由**剧情库**、**人物库**、**语料库**和**创新元素库**四大类构成。
>
> 1.  **剧情库**：结构化地存储剧情单元。
>     *   **核心字段**：包含来源信息、出场人物及关系、剧情类型（如"打脸"、"夺宝"）、冲突、结局影响，以及基于"段评"热度和故事理论的**质量评分**。
>     *   **召回方式**：我们放弃了繁琐的标签体系，全面采用**语义召回**。我们为每个剧情单元撰写自然语言梗概，当需要剧情时，用"**主角刚小有名气，需要一个打脸反派的剧情**"这样的自然语言进行检索。系统召回若干候选后，再由AI Agent结合**当前上下文**（如主角团状态、反派状态、阶段性目标等）进行最终决策。
> 2.  **人物库**：人物库并非独立存在，而是**剧情库的核心配套设施**，旨在让AI更好地理解剧情。
>     *   **功能**：记录核心角色的"脸谱化描述"、功能类型、背景、关系网络等。其核心作用是**将剧情单元中缺失的、隐含的上下文信息显性化**。
>     *   **示例**：一个剧情单元只写了"角色A和角色B发生冲突"，但它们是"同族死敌"这一背景信息在前文。人物库会捕捉并补充这一信息，确保AI在续写时能正确理解其动机和行为逻辑。
> 3.  **语料库**：收录高赞"**段评**"密集区的原文，并由AI标注**使用场景**（如"怼人场景"、"见到美女的反应"）。在**人工精修**环节，编辑可按需调用这些富有"网感"和"梗"的语料，融入正文，弥补AI在高级趣味和幽默感上的短板，提升文章表现力。
> 4.  **创新元素库**：专门收录最新爆款书的**开篇切入点**和**主角人设**，用于开篇创作阶段的"旧瓶装新酒"。

### 问：如何将不同书的素材融合到新故事中，并保证剧情连接的逻辑自洽？

> 这是生产线的核心难点之一，我们通过**"三步改编"**和**"关联强度判断"**两套机制来系统性解决。
>
> #### 1. 三步走，实现剧情的无缝改编
>
> 我们设计了三步保障机制，确保来自不同作品的剧情、人物和场景能够被理顺并整合进新故事中：
>
> *   **第一步：深度理解与信息显性化**：在将爆款书拆解为剧情素材时，我们进行深度理解，而非简单提取。
>     *   **实体与关系识别**：自动识别每个剧情单元内的核心实体（**人物、地点、家族、阵营**），并构建它们的关系图谱。
>     *   **隐性信息显性化**：将原文中隐含的信息明确标注出来。例如，如果原文暗示人物A和B来自同一敌对家族，我们会为这两个角色明确打上"同族"、"敌对"的标签。这为后续的精准匹配和改编打下了坚实基础。
>
> *   **第二步：基于上下文的匹配度评估**：从素材库召回剧情单元时，我们会进行严格的匹配度评估，对比**剧情单元的属性**和**新故事的当前上下文**：
>     *   **影响程度匹配**：母本（Template）当前阶段要求主角"声名大噪"，那么召回的剧情单元也必须具备能引发"声名大噪"的潜力。
>     *   **角色与场景匹配**：剧情单元涉及的角色数量、发生的场景（都市、仙侠）等，是否与新书的世界观和主角团设定存在巨大偏离？
>     只有高匹配度的剧情单元才会被选为候选。
>
> *   **第三步：AI驱动的适应性改编**：最后，我们将**新书的当前状态**（世界观、主角团信息）、**剧情要求**（如"主角在此事件后获得XX技能"）和**候选剧情单元**一起交给大模型。得益于GPT-4o等模型的强大能力，它能够自动完成角色替换、逻辑微调，将剧情无缝融入当前故事线。
>
> #### 2. 区分关联强度，实现剧情的灵活连接
>
> 为了保证不同剧情单元之间连接的流畅与合理，我们对其**关联强度**进行区分：
>
> *   **强关联剧情 (剧情链)**：指那些在逻辑上存在严格先后顺序、不可拆分的剧情序列。例如，综艺比赛的"初赛" -> "复赛" -> "决赛"。这类剧情我们会预先串联成"**剧情链**"进行整体调用，保证其逻辑完整性。
>
> *   **弱关联剧情**：指两个独立的剧情单元，它们之间没有固定的因果关系，但可以通过合理的"**编排**"建立逻辑连接。例如，"参加直播"和"录音棚录歌"这两个单元：
>     *   可以编排为：因**直播**唱歌爆火，所以去**录音棚**录制单曲。
>     *   也可以编排为：在**录音棚**录歌的片段泄露引发关注，所以开**直播**回应热度。
>
> 只要不是强关联，我们就可以利用AI，在剧情单元之间创造出合乎情理的因果链条，实现情节的灵活组合与平滑过渡。这套成熟的方案确保了故事既能保持主线节奏，又能在局部剧情上丰富多变。

---

## 三、 AI核心技术与实现

### 问：能否从技术架构的视角，描绘一下整个AI网文生产线的全貌？

> 当然。整个生产线基于**微服务架构**构建，确保各模块高内聚、低耦合，并能独立伸缩，保证了系统的健壮性和可扩展性。
> 
> *   **核心服务层**:
>     *   **MCP Server (Model Communicate Protocol Server)**: 这是系统的核心通信协议服务端，为追求高性能，我们采用 **Golang** 开发。它负责响应 Agent 在工作流中发起的上下文(Context)请求，是连接大模型、私有数据与具体业务场景的桥梁。它主要提供两大核心能力：
>         1.  **记忆与状态管理**：通过 **Redis** 的 Key-Value 存储实现对角色卡、世界观等动态状态的毫秒级读写，确保续写时能快速获取最新上下文。
>         2.  **剧情智能检索**：我们采用了成熟的**混合式检索 (Hybrid Search)** 方案。在工程实现上，我们将传统的 **Elasticsearch** 用于关键词和元数据（如剧情类型、角色标签）的精确匹配，同时引入了**向量检索技术**来解决语义相似度匹配的问题。这样做的好处是，我们可以用自然语言（如"主角需要一个打脸反派的剧情"）作为查询，召回语义上相关但关键词完全不同的剧情单元。MCP Server 将这两种检索能力**封装为统一的 gRPC 和 RESTful API**，对上游的 Agent 调用者屏蔽了底层的复杂性。
>     *   **LLM 网关 (LLM Gateway)**: 作为一个统一的中间件，负责管理对不同大模型的调用（如 OpenAI API、Claude、以及本地部署的开源模型）。它处理**密钥管理、请求路由、负载均衡、结果缓存 (使用 Redis)** 等，既降低了业务逻辑与特定模型的耦合，也通过缓存策略有效降低了调用成本。
>     *   **工作流编排引擎 (Workflow Engine)**: 我们采用 **Argo Workflows** 或类似的云原生工作流引擎，将整个网文生产流程定义为一个 **DAG (有向无环图)**。从选题、大纲生成、章节写作到质检，每个环节都是图上的一个节点。引擎负责任务调度、依赖管理、失败重试，实现了生产流程的自动化与可视化。
>
> *   **支撑系统与基础设施**:
>     *   **数据采集系统**: 即我在之前工作经验中负责的**全网内容池**，为素材库提供源源不断的数据输入。
>     *   **人机协作平台**: 面向内部编辑和外部协作者的前端应用，用于任务分配、内容精修和质量审核。

>
> **技术架构图：**
>
> ```mermaid
> graph TD;
>     subgraph "AI 网文生产线技术架构"
>         direction TB
> 
>         subgraph "人机协作层"
>             A["<b>人机协作平台</b><br/>(面向内部编辑/外部协作者)"];
>         end
> 
>         subgraph "工作流编排层"
>             B["<b>工作流编排引擎 (Workflow Engine)</b><br/>(e.g., Argo Workflows)<br/>定义和调度整个生产流程 (DAG)"];
>         end
> 
>         subgraph "核心服务层 (Microservices)"
>             C["<b>智能体 (Agent)</b><br/>执行具体任务节点<br/>采用 ReAct 模式进行决策与行动"];
>             D["<b>MCP Server (Golang)</b><br/>上下文管理与记忆检索核心<br/>提供 gRPC / RESTful API"];
>             E["<b>LLM 网关 (LLM Gateway)</b><br/>统一管理对不同大模型的调用"];
>         end
> 
>         subgraph "数据与模型层"
>             F["<b>状态知识库 (State-based)</b><br/>(Redis)<br/>存储角色/世界观等当前快照"];
>             G["<b>情节记忆库 (Episodic)</b><br/>(Elasticsearch + Vector DB)<br/>存储结构化的剧情单元"];
>             H["<b>大语言模型 (LLMs)</b><br/>OpenAI, Claude, 开源模型等"];
>             I["<b>结果缓存 (Cache)</b><br/>(Redis)<br/>缓存 LLM 调用结果"];
>         end
> 
>         subgraph "数据源"
>             J["<b>数据采集系统</b><br/>(爬虫/采买)<br/>为素材库提供源源不断的数据输入"];
>         end
> 
>         %% Connections
>         A -- "发起/监控/精修任务" --> B;
>         B -- "调度任务节点" --> C;
>         C -- "1. 思考并查询上下文" --> D;
>         C -- "2. 思考并请求生成" --> E;
>         D -- "读/写状态" --> F;
>         D -- "混合式检索剧情" --> G;
>         E -- "路由/负载均衡" --> H;
>         E -- "读/写缓存" --> I;
>         J -- "构建/更新素材库" --> G;
>         J -- "构建/更新素材库" --> F;
>         H -- "生成内容" --> E;
>         E -- "返回结果" --> C;
>         C -- "完成任务" --> B;
>         B -- "产出初稿" --> A;
>     end
> ```

### 问：如何解决AI网文的长上下文记忆问题，并保证人物性格不崩？

> 我们通过"**宏观大纲锁定**"和"**微观状态管理**"相结合的方式，解决长线叙事中的记忆与一致性难题。
>
> 1.  **宏观大纲锁定**：在动笔前，我们会严格对标一本爆款书的框架，规划好全书的核心事件、剧情阶段与节奏转折。每个角色的出场时机、在特定阶段应达成的状态都被预先"锁死"在剧情单元的格栅里。这如同为故事铺设了轨道，从根本上保证了长线剧情不失控、不跳脱。后续我们还会人工审阅大纲的整体逻辑，确保其能自圆其说。
>
> 2.  **微观状态管理 (State Management)**：我们为每个核心剧情单元进行精细化的状态管理。
>     *   **结构化记录**：我们为核心角色建立"**角色卡 (Character Card)**"，结构化记录其动态信息。技术上，"角色卡"的核心字段（如身份、能力、物品、位置）以 **Key-Value** 形式存储在 **Redis** 中，例如 `character:123:location`，以实现低延迟读写。同时，我们也管理地图、势力分布等"**世界观状态**"。
>     *   **按需精准调用**：真正的难点在于信息的调用。我们不采用粗暴地将全文或全部状态信息塞给模型的方式，而是建立了一套 **Agent 按需查询**机制。
>         *   **智能判断字段**：我们会根据字段类型，决定是返回历史记录（如角色的所有过往身份）还是最新状态（如角色的当前位置）。
>         *   **Agent自主查询**：在写作具体情节时，Agent会自主判断"我需要哪些信息？"，然后向 **MCP Server** 发起精准的 API 调用，例如 `POST /mcp/query_context`，载荷中描述了当前情节和所需查询的实体。MCP Server 会解析请求，分别查询 **Redis**（用于状态）和**我们封装的检索服务**（用于剧情），然后智能组装出最精简的上下文返回给 Agent。例如，写一场**A和B的打斗戏**，Agent可能只需要查询"**A的技能与法法宝**"、"**B的技能与法宝**"以及"**当前的地图**"就足够了。
>
> 通过这种"**大纲约束 + 精准供给**"的模式，既能保证AI获得必要信息，又避免了无关信息干扰，实现了高效、一致的情节推进和人物塑造。

### 问：当Agent查询"记忆"时，是如何保证在海量历史信息中，精确地只召回当前章节最相关的上下文，从而避免污染Prompt、浪费Token？

> 这是我们记忆系统的核心技术所在。我们通过构建"**状态知识库**"与"**情节记忆库**"，并设计了一套**多路召回与排序融合**的策略，来确保上下文的精准性。
>
> 1.  **状态知识库 (State-based Knowledge)：回答"是什么"**
>     *   **作用**：存储实体（人物、物品、势力）的**当前快照信息**。例如，主角的最新等级、持有的物品、当前的位置。
>     *   **技术实现**：使用 **Redis** 的 Key-Value 存储，例如 `character:123:level`, `character:123:inventory`。
>     *   **召回方式**：当新章节大纲中明确提到某个实体时，Agent 会直接通过 Key 进行**精确、低延迟**的查询，获取其最新状态。这是最高优先级、最基础的信息。
>
> 2.  **情节记忆库 (Episodic Memory)：回答"发生过什么"**
>     *   **作用**：存储已经发生过的、结构化的**剧情单元**。这是信息量最大、也最容易产生干扰的部分。
>     *   **召回策略 (多路召回)**：我们不依赖单一的检索方式，而是并行采用多种策略从情节库中召回候选记忆，每种策略各有侧重：
>         *   **a. 时间衰减加权检索 (Recency-Weighted Search)**：这是最重要的一路。系统会优先检索**最近发生的剧情单元**（如过去5-10章），并给予最高权重。因为在网文叙事中，近期事件的关联性通常最强。
>         *   **b. 实体关联检索 (Entity-Centric Search)**：如果当前章节的核心是人物A和人物B的互动，系统会专门检索过去所有与"A和B同时出现"或"对A和B关系产生重大影响"的剧情单元。这能有效保证人物关系发展的一致性。
>         *   **c. 语义相似度检索 (Semantic Search)**：基于当前章节的**梗概**，通过向量检索寻找历史上**情节主旨最相似**的剧情单元。例如，当前要写"主角拍卖会捡漏"，此路召回可能会找到历史上"主角地摊淘宝"的情节，这有助于AI模仿过去的成功写法和铺垫方式，但此路权重较低，主要起补充作用。
>
> 3.  **Rerank & Fushion (重排与融合)**
>     *   **工作机制**：多路召回的候选记忆（剧情单元）并不会直接交给大模型。我们会通过一个轻量级的**排序模型 (Reranker)**，根据"**相关性**"和"**重要性**"对这些记忆进行二次打分和排序。
>     *   **智能组装**：最后，系统会选取排序最高的 Top-K 个剧情单元，结合从"状态知识库"中查到的实体快照，**智能地组装**成一段最精简、信息量最高的上下文，最终提供给大模型用于生成。
>
> 通过这套"**精确查询+多路召回+重排序**"的组合拳，我们最大限度地模拟了人类作者在创作时的联想过程，既能记起"上回书说到"的直接情节，又能关联起"很久之前埋下的伏笔"，同时过滤掉大量无关信息，实现了效率、成本和质量的平衡。

### 问：AI写作的Prompt要点有哪些？如何迭代优化以避免"AI味"？

> "AI味"本质是模型惯性导致的表达匮乏与逻辑浅薄。我们摒弃了简单的指令，进化到一套**高度结构化、可量化的"公式级"Prompt体系**，从四个层面系统性地解决此问题：
> 
> 1.  **指令层：定义输出规则与硬性约束**。这是最基础的层面，用于规避AI最常见的错误。我们会明确规定：
>     *   **输出格式**：如"只输出正文，禁止回答问题"、"段落间空一行"、"所有对话必须独立成段"等。
>     *   **硬性规避**：建立"禁用词表"（如"眼中闪过一丝XX"、"嘴角勾起一抹弧度"）和"禁用句式"（如比喻、拟人、并列结构）。
>     *   **量化指标**：设定具体的技术指标，如"确保AI检测工具（如朱雀v3）置信度低于30%"、"单句成段的比例控制在50%-65%"，以适配手机阅读习惯。
> 2.  **技巧层：注入专业写作方法论**。我们将专业的写作技巧转化为AI可理解的指令，核心是贯彻"**Show, Don't Tell**"原则：
>     *   **五感描写**：要求AI调用五感（视觉、听觉、嗅觉、味觉、触觉）进行环境与细节描写。
>     *   **侧面呈现情感**：严禁直接陈述情感（如"他很愤怒"），而是要求通过**动作、神态、心理活动、环境烘托**来"呈现"情感（如"他猛地捏紧拳头，指节因用力而发白"）。
>     *   **电影镜头感**：运用场景切换、特写、远景等指令，引导AI生成富有画面感的文字。
>     *   **角色动机清晰化**：要求在刻画人物时，必须让读者能清晰了解角色的**当前现状、心理动机和小目标**，并通过配角反衬主角。
> 3.  **公式层：量化网文核心爽点**。这是我们区别于常规Prompt的核心。我们将网文的创作规律，抽象为一系列可执行的"**创作公式**"，并融入Prompt，引导AI生成高爽感内容：
>     *   **情绪公式**：如 `期待感 = 目标 + 可能性 + 障碍 + 延迟满足`；`悬念 = 未知 + 威胁/诱惑 + 延迟揭示`。
>     *   **爽点公式**：定义 `爽 = 压力的积累 + 冲突的深化 + 释放的满足`，并拆解为小爽与大爽的达成路径。
>     *   **信息差公式**：`信息差 = A（已知）+ B（未知）+ C（信息不对称导致的冲突）`，以此制造悬念与反转。
> 4.  **上下文层：提供全面的创作背景**。在每次生成任务中，我们都会动态提供丰富的上下文信息，包括但不限于：
>     *   **角色卡**：包含人设、动机、能力、人际关系等。
>     *   **世界观设定**与**当前剧情细纲**。
>     *   **题材风格**与**爆款文风参考**，让AI能够模仿特定爆款作品的写作风格。
> 
> 我们搭建了"**Prompt实验平台**"，包含约200条测试任务。我们会定期复盘线上生成的内容，一旦发现新的"AI味"问题，就将其加入规避列表，并持续迭代优化这套四层Prompt体系，形成一个持续进化的闭环。该平台支持 Prompt 模板的版本化管理 (类似 Git)，以及自动化 A/B 测试，能够量化评估不同 Prompt 版本在关键指标上的表现差异。

### 问：产线中工作流（Workflow）和智能体（Agent）如何分工？

> 在我们的生产线中，Workflow和Agent相辅相成、各司其职。Agent是新近落地应用的概念。
>
> *   **Workflow (工作流)**：负责**宏观流程的把控**。它由 **Argo Workflows** 等编排引擎驱动，以 **DAG (有向无环图)** 的形式清晰地定义了从大纲到正文的每一个固定步骤（A→B→C），保证了产物的稳定性和可控性，是规模化生产的基础。
> *   **Agent (智能体)**：负责**需要自主决策的子任务**。它通常是 Workflow 某个节点（如"生成章节"节点）调用的一个**具备复杂内部逻辑的函数或服务**。Agent 会采用类似 **ReAct (Reason + Act)** 的模式执行任务：首先进行**思考**（"根据大纲，下一步主角该做什么？"），然后选择**行动**（如调用 MCP Server 查询记忆、调用 LLM 网关生成初稿），再根据行动结果进行下一轮思考，直到任务完成。这种灵活性和智能性极大地提升了系统的容错率和内容质量。
>
> 我们自研的`MCP Server`主要服务于Agent，为其提供**记忆和剧情的智能召回**能力。

### 问：如何选择使用商业和开源大模型？

> 我们采用**商业闭源大模型与开源大模型结合**的策略，以实现成本和效果的最佳平衡。所有模型调用都通过统一的 **LLM 网关**进行。
>
> *   **开源模型 (如 阿里通义千问)**：用于**逻辑相对简单、任务量大**的环节，如**情节点提取、人名等实体的占位符替换、部分文本优化**等。这些任务对推理能力要求不高，使用开源模型性价比更高。
> *   **商业模型 (如 Claude、GPT系列)**：用于**核心的、需要强大推理和创意能力**的环节，如**深度剧情改编、高质量正文生成**等。
>
> 我们的模型选型是开放的，主要依据是**任务的复杂程度、对结果质量的要求以及模型自身的特性**。

---

## 四、 质量保障与规模化

### 问：产线的质检体系是如何自动化的？

> 我们的质检体系是**自动化检测为主，人工审查为辅**，旨在高效保障故事的连贯性和角色的统一性。
>
> 1.  **自动化检测流程**：
>     *   **多维度检查**：AI会自动进行多维度扫描。微观上，检测**单章**内的逻辑（如天气、时间、人物指代的矛盾）；中观上，以**5章**为单位检查短期剧情弧光是否合理；宏观上，串联全文梗概检查长线逻辑。
>     *   **人物一致性 (OOC) 检测**：提取核心角色的行为与对话，与其"角色卡"中的人设进行比对，预警并标记可能出现的OOC（Out of Character，角色性格崩坏）风险。
> 2.  **自研辅助工具**：AI检查后会生成一份类似**代码 Diff Review**的"校验报告"。报告会清晰地标出问题、说明原因并给出修改建议，辅助内容团队高效地进行二次确认和人工修正。

### 问：如何界定人和AI的分工？如何管理外部协作者？

> **人机边界**：
> *   **AI (80%)**：定位是高效的"**助理写手**"，负责执行层的工作（约占80%）。它将数据、素材、创意快速生成为内容初稿，并执行自动化质检。
> *   **人 (20%)**：定位是"**制作人/主编**"，负责决策层的工作（约占20%）。人的核心价值在于**创意、审美和方向把控**，如IP立项、核心大纲排布、最终质量审核等。
>
> **外部协作管理**：为实现规模化，我们不仅依赖内部编辑，还积极整合外部协作者。我们与教育平台合作，将其AI课程的学员转化为我们的协作者。
> *   **课程与任务联动**：我们为课程设计教学大纲，而学员则会参与到我们的真实写作任务中。
> *   **测试与激励**：我们会根据学员的基础测试和任务完成情况，给予相应的**绩效激励**和**署名权**，构建一个人人可参与的内容生态。

---

## 五、 商业思考与未来展望

### 问：为什么选择做产线而不是工具？如果SaaS化，产品形态如何？

> **为什么做产线**：
> 1.  **商业模式**：在国内市场，**内容付费**的模式比**工具付费**更为成熟，用户基础更好。
> 2.  **版权归属**：做产线，我们可以拥有**自主版权内容**，这符合公司的核心战略。而做工具，产出内容的版权通常归属于用户（当然，具体取决于公司的战略选择）。
>
> **SaaS化产品设想**：
> *   **产品形态**：一个一站式的AI网文创作平台，引导用户走完从**[立意参考] -> [大纲生成] -> [素材填充] -> [正文写作] -> [智能精修]**的全流程。本质上是将我们成熟的产线能力，通过友好的UI/UX开放给外部用户。
> *   **核心挑战**：**素材的合规化与版权问题**。需要设计一套机制，确保平台提供给用户的各种素材（剧情单元、人设等）是经过授权或合规处理的，避免法律风险。

### 问：如何评价当前AI网文的质量、潜力与短板？

> **保证质量与规模化**：
> *   **保证质量**：依赖三要素：高质量的**素材库**作为输入、沉淀有效的**方法论**融入产线、清晰的**人机协同**分工。
> *   **实现规模化**：依赖三大支柱：**剧情单元化**的架构设计、标准化的**流程解耦**、高效的内外部**协同体系**。
>
> **AI网文的明显短板**：
> 1.  **长线伏笔和复杂结构**：对于像《诡秘之主》那样贯穿全文、草蛇灰线的精巧结构，AI在长线逻辑掌控上仍有短板。
> 2.  **高级趣味与幽默感**：AI能写出流畅爽点，但在创造真正**幽默、有"梗"**的趣味性内容上，还很刻板。
> 3.  **顶级人物塑造**：在塑造具有深刻人性、矛盾感和独特魅力的顶级人物方面，AI的创作仍显单薄，容易标签化。